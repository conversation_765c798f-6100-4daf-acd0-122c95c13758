import * as fs from 'fs';
import * as path from 'path';
import { Database } from '../database/Database';

export interface BackupInfo {
  id: string;
  filename: string;
  timestamp: Date;
  size: number;
  description: string;
}

export interface DuplicateAction {
  id: string;
  timestamp: Date;
  action: 'delete' | 'merge' | 'keep';
  groupId: string;
  agenciesAffected: number[];
  keptAgencyId?: number | undefined;
  description: string;
}

export class BackupService {
  private static readonly BACKUP_DIR = 'backups';
  private static readonly LOG_FILE = 'duplicate_actions.log';

  public static async ensureBackupDirectory(): Promise<void> {
    if (!fs.existsSync(this.BACKUP_DIR)) {
      fs.mkdirSync(this.BACKUP_DIR, { recursive: true });
    }
  }

  public static async createBackup(description: string = 'Manual backup'): Promise<BackupInfo> {
    await this.ensureBackupDirectory();
    
    const timestamp = new Date();
    const timestampStr = timestamp.toISOString().replace(/[:.]/g, '-').split('T')[0] + '_' +
                        (timestamp.toISOString().split('T')[1]?.split('.')[0]?.replace(/:/g, '-') || '');
    const backupId = `backup_${timestampStr}`;
    const filename = `${backupId}.db`;
    const backupPath = path.join(this.BACKUP_DIR, filename);
    
    // Copy the database file
    const originalDbPath = 'clutchscrape.db';
    if (!fs.existsSync(originalDbPath)) {
      throw new Error('Original database file not found');
    }
    
    fs.copyFileSync(originalDbPath, backupPath);
    
    const stats = fs.statSync(backupPath);
    
    const backupInfo: BackupInfo = {
      id: backupId,
      filename,
      timestamp,
      size: stats.size,
      description
    };
    
    // Log the backup creation
    await this.logAction({
      id: `backup_${Date.now()}`,
      timestamp,
      action: 'keep',
      groupId: 'backup',
      agenciesAffected: [],
      description: `Backup created: ${filename} - ${description}`
    });
    
    return backupInfo;
  }

  public static async listBackups(): Promise<BackupInfo[]> {
    await this.ensureBackupDirectory();
    
    const backups: BackupInfo[] = [];
    const files = fs.readdirSync(this.BACKUP_DIR);
    
    for (const file of files) {
      if (file.endsWith('.db')) {
        const filePath = path.join(this.BACKUP_DIR, file);
        const stats = fs.statSync(filePath);
        
        // Extract timestamp from filename
        const timestampMatch = file.match(/backup_(\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2})/);
        let timestamp = stats.mtime;
        
        if (timestampMatch && timestampMatch[1]) {
          const timestampStr = timestampMatch[1].replace(/_/g, 'T').replace(/-/g, ':');
          timestamp = new Date(timestampStr + '.000Z');
        }
        
        backups.push({
          id: file.replace('.db', ''),
          filename: file,
          timestamp,
          size: stats.size,
          description: 'Database backup'
        });
      }
    }
    
    return backups.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  public static async restoreBackup(backupId: string): Promise<void> {
    const backupPath = path.join(this.BACKUP_DIR, `${backupId}.db`);
    
    if (!fs.existsSync(backupPath)) {
      throw new Error(`Backup file not found: ${backupId}`);
    }
    
    // Create a backup of current state before restoring
    await this.createBackup('Pre-restore backup');
    
    // Restore the backup
    const originalDbPath = 'clutchscrape.db';
    fs.copyFileSync(backupPath, originalDbPath);
    
    // Log the restore action
    await this.logAction({
      id: `restore_${Date.now()}`,
      timestamp: new Date(),
      action: 'keep',
      groupId: 'restore',
      agenciesAffected: [],
      description: `Database restored from backup: ${backupId}`
    });
  }

  public static async logAction(action: DuplicateAction): Promise<void> {
    await this.ensureBackupDirectory();
    
    const logPath = path.join(this.BACKUP_DIR, this.LOG_FILE);
    const logEntry = JSON.stringify(action) + '\n';
    
    fs.appendFileSync(logPath, logEntry);
  }

  public static async getActionLog(): Promise<DuplicateAction[]> {
    const logPath = path.join(this.BACKUP_DIR, this.LOG_FILE);
    
    if (!fs.existsSync(logPath)) {
      return [];
    }
    
    const logContent = fs.readFileSync(logPath, 'utf-8');
    const lines = logContent.trim().split('\n').filter(line => line.length > 0);
    
    return lines.map(line => {
      try {
        const action = JSON.parse(line);
        action.timestamp = new Date(action.timestamp);
        return action;
      } catch (error) {
        console.warn('Failed to parse log entry:', line);
        return null;
      }
    }).filter(action => action !== null) as DuplicateAction[];
  }

  public static async cleanupOldBackups(keepCount: number = 10): Promise<void> {
    const backups = await this.listBackups();
    
    if (backups.length <= keepCount) {
      return;
    }
    
    const toDelete = backups.slice(keepCount);
    
    for (const backup of toDelete) {
      const backupPath = path.join(this.BACKUP_DIR, backup.filename);
      if (fs.existsSync(backupPath)) {
        fs.unlinkSync(backupPath);
        console.log(`Deleted old backup: ${backup.filename}`);
      }
    }
  }

  public static formatFileSize(bytes: number): string {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  }

  public static async getDatabaseStats(): Promise<{ size: number; agencyCount: number }> {
    const dbPath = 'clutchscrape.db';
    
    if (!fs.existsSync(dbPath)) {
      return { size: 0, agencyCount: 0 };
    }
    
    const stats = fs.statSync(dbPath);
    
    // Get agency count from database
    const db = new Database();
    const agencyCount = await new Promise<number>((resolve, reject) => {
      (db as any).db.get('SELECT COUNT(*) as count FROM agencies', (err: any, row: any) => {
        if (err) {
          reject(err);
        } else {
          resolve(row.count);
        }
      });
    });
    
    db.close();
    
    return {
      size: stats.size,
      agencyCount
    };
  }
}
