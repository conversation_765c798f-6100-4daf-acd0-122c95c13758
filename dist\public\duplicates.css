/* Duplicate Management Styles */

.breadcrumb {
    margin-top: 15px;
}

.breadcrumb a {
    color: #3498db;
    text-decoration: none;
    font-weight: 500;
}

.breadcrumb a:hover {
    text-decoration: underline;
}

/* Detection Section */
.detection-section {
    background: white;
    padding: 25px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.detection-section h2 {
    color: #2c3e50;
    margin-bottom: 20px;
}

.criteria-form {
    max-width: 100%;
}

.criteria-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin-bottom: 25px;
}

.criteria-group {
    padding: 20px;
    border: 2px solid #ecf0f1;
    border-radius: 8px;
    background: #f8f9fa;
}

.criteria-group h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.1em;
}

.checkbox-label {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    cursor: pointer;
    font-size: 14px;
}

.checkbox-label input[type="checkbox"] {
    margin-right: 10px;
    transform: scale(1.2);
}

.threshold-input {
    margin-top: 15px;
    padding: 10px;
    background: white;
    border-radius: 4px;
    border: 1px solid #ddd;
}

.threshold-input label {
    display: block;
    font-size: 13px;
    color: #555;
    margin-bottom: 8px;
}

.threshold-input input[type="range"] {
    width: 100%;
    margin-bottom: 5px;
}

.threshold-input span {
    font-weight: bold;
    color: #3498db;
}

.action-buttons {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.primary-btn {
    background-color: #3498db;
    color: white;
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s;
}

.primary-btn:hover {
    background-color: #2980b9;
}

.secondary-btn {
    background-color: #95a5a6;
    color: white;
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s;
}

.secondary-btn:hover {
    background-color: #7f8c8d;
}

/* Loading Section */
.loading-section {
    text-align: center;
    padding: 40px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Results Section */
.results-section {
    background: white;
    padding: 25px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    flex-wrap: wrap;
    gap: 15px;
}

.results-summary {
    background: #e8f4fd;
    padding: 10px 15px;
    border-radius: 6px;
    color: #2c3e50;
    font-weight: 500;
}

/* Backup Section */
.backup-section {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 25px;
}

.backup-section h3 {
    color: #856404;
    margin-bottom: 10px;
}

.backup-section p {
    color: #856404;
    margin-bottom: 15px;
}

.backup-controls {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    align-items: center;
}

.backup-controls input[type="text"] {
    flex: 1;
    min-width: 200px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.backup-btn {
    background-color: #28a745;
    color: white;
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
}

.backup-btn:hover {
    background-color: #218838;
}

/* Bulk Actions */
.bulk-actions {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 25px;
}

.bulk-actions h3 {
    color: #2c3e50;
    margin-bottom: 15px;
}

.bulk-controls {
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
}

.bulk-controls select {
    flex: 1;
    min-width: 200px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.bulk-btn {
    background-color: #17a2b8;
    color: white;
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
}

.bulk-btn:hover:not(:disabled) {
    background-color: #138496;
}

.bulk-btn:disabled {
    background-color: #6c757d;
    cursor: not-allowed;
}

/* Duplicate Groups */
.duplicate-groups {
    margin-bottom: 25px;
}

.duplicate-group {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 20px;
    overflow: hidden;
}

.group-header {
    background: #f8f9fa;
    padding: 15px 20px;
    border-bottom: 1px solid #dee2e6;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.group-header:hover {
    background: #e9ecef;
}

.group-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.confidence-badge {
    background: #28a745;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.confidence-badge.medium {
    background: #ffc107;
    color: #212529;
}

.confidence-badge.low {
    background: #dc3545;
}

.expand-icon {
    font-size: 18px;
    transition: transform 0.3s;
}

.expand-icon.expanded {
    transform: rotate(180deg);
}

.group-content {
    display: none;
    padding: 20px;
}

.group-content.expanded {
    display: block;
}

.agencies-comparison {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.agency-card-compare {
    border: 2px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
    background: white;
    position: relative;
}

.agency-card-compare.selected {
    border-color: #28a745;
    background: #f8fff9;
}

.agency-card-compare .card-header {
    margin-bottom: 12px;
}

.agency-card-compare .agency-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 5px;
}

.agency-card-compare .rating-reviews {
    color: #6c757d;
    font-size: 14px;
}

.agency-card-compare .details {
    font-size: 13px;
    color: #6c757d;
}

.agency-card-compare .details div {
    margin-bottom: 4px;
}

.select-radio {
    position: absolute;
    top: 10px;
    right: 10px;
}

.group-actions {
    border-top: 1px solid #dee2e6;
    padding-top: 15px;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.action-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    font-size: 14px;
}

.keep-best-btn {
    background-color: #28a745;
    color: white;
}

.keep-selected-btn {
    background-color: #17a2b8;
    color: white;
}

.merge-btn {
    background-color: #ffc107;
    color: #212529;
}

.skip-btn {
    background-color: #6c757d;
    color: white;
}

.action-btn:hover {
    opacity: 0.9;
}

.action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Process Section */
.process-section {
    text-align: center;
    padding-top: 20px;
    border-top: 2px solid #dee2e6;
}

.process-btn {
    background-color: #dc3545;
    color: white;
    padding: 15px 30px;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s;
}

.process-btn:hover:not(:disabled) {
    background-color: #c82333;
}

.process-btn:disabled {
    background-color: #6c757d;
    cursor: not-allowed;
}

/* Status Messages */
.status-message {
    margin-top: 15px;
    padding: 10px 15px;
    border-radius: 4px;
    font-weight: 500;
}

.status-message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status-message.info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 8px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: #2c3e50;
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6c757d;
}

.close-btn:hover {
    color: #2c3e50;
}

.modal-body {
    padding: 20px;
}

.confirm-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 20px;
}

.danger-btn {
    background-color: #dc3545;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
}

.danger-btn:hover {
    background-color: #c82333;
}

/* Backup List */
.backup-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    margin-bottom: 10px;
    background: #f8f9fa;
}

.backup-info {
    flex: 1;
}

.backup-info .backup-name {
    font-weight: 600;
    color: #2c3e50;
}

.backup-info .backup-details {
    font-size: 13px;
    color: #6c757d;
    margin-top: 4px;
}

.backup-actions {
    display: flex;
    gap: 8px;
}

.restore-btn {
    background-color: #17a2b8;
    color: white;
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

.restore-btn:hover {
    background-color: #138496;
}

/* Responsive Design */
@media (max-width: 768px) {
    .criteria-grid {
        grid-template-columns: 1fr;
    }

    .results-header {
        flex-direction: column;
        align-items: stretch;
    }

    .backup-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .bulk-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .agencies-comparison {
        grid-template-columns: 1fr;
    }

    .group-actions {
        flex-direction: column;
    }

    .action-buttons {
        flex-direction: column;
    }

    .backup-item {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .backup-actions {
        justify-content: center;
    }
}
