# Clutch Agencies Web Explorer

A simple web application for exploring the recruiting agencies data scraped from Clutch.co. This web interface provides an intuitive way to browse, search, and filter through the agency database.

## Features

### 🔍 **Data Display**
- **Table View**: Clean tabular display of all agency information
- **Card View**: Card-based layout for better mobile experience
- **Responsive Design**: Works seamlessly on desktop, tablet, and mobile devices

### 🔎 **Search Functionality**
- **Global Search**: Search across agency names, descriptions, locations, and websites
- **Real-time Search**: Results update as you type (with 500ms debounce)
- **Keyboard Shortcuts**: 
  - `Ctrl+K` (or `Cmd+K` on Mac) to focus search
  - `Esc` to clear search

### 🎛️ **Filter Options**
- **Location Filter**: Dropdown with all available locations
- **Rating Range**: Filter by minimum and maximum ratings (0-5 stars)
- **Review Count Range**: Filter by number of reviews
- **Website Filter**: Option to show only agencies with websites
- **Combined Filters**: All filters work together for precise results

### 📱 **Mobile-Friendly Features**
- **Responsive Table**: On small screens, table transforms into stacked cards
- **Touch-Friendly**: Optimized button sizes and interactions for mobile
- **Swipe Navigation**: Easy navigation on touch devices

### 🎨 **User Experience**
- **View Toggle**: Switch between table and card views
- **Pagination**: Navigate through large result sets
- **Loading Indicators**: Visual feedback during searches
- **Expandable Descriptions**: Click "Show More" for full agency descriptions
- **External Links**: Direct links to agency websites and Clutch profiles

## Getting Started

### Prerequisites
- Node.js (v14 or higher)
- The existing SQLite database (`clutchscrape.db`)

### Installation
The web application uses the existing project dependencies. No additional installation required.

### Running the Web Application

#### Development Mode
```bash
npm run web
```
This starts the development server with TypeScript compilation on-the-fly.

#### Production Mode
```bash
npm run web:build
```
This builds the TypeScript code and static files, then starts the production server.

### Accessing the Application
Once started, open your browser and navigate to:
```
http://localhost:3000
```

## Database Connection

The web application automatically connects to the existing `clutchscrape.db` SQLite database in the project root. It provides read-only access to the data, ensuring the original database remains unchanged.

### Database Schema
The application reads from the `agencies` table with the following fields:
- `id`: Unique identifier
- `name`: Agency name
- `rating`: Star rating (0-5)
- `review_count`: Number of reviews
- `location`: Geographic location
- `description`: Agency description
- `contact_phone`: Phone number
- `contact_website`: Website URL
- `clutch_profile_url`: Clutch.co profile link
- `data_json`: Additional metadata
- `scraped_at`: Timestamp of data collection

## API Endpoints

### GET `/`
Main web interface with search and filter capabilities.

**Query Parameters:**
- `search`: Text search across multiple fields
- `location`: Filter by specific location
- `minRating`: Minimum rating filter
- `maxRating`: Maximum rating filter
- `minReviews`: Minimum review count
- `maxReviews`: Maximum review count
- `page`: Page number for pagination

### GET `/api/agencies`
JSON API endpoint returning filtered agency data.

**Response Format:**
```json
{
  "agencies": [...],
  "totalPages": 10,
  "currentPage": 1,
  "totalResults": 380
}
```

## Technology Stack

- **Backend**: Node.js with Express.js
- **Database**: SQLite3
- **Templating**: EJS (Embedded JavaScript)
- **Styling**: Custom CSS with responsive design
- **JavaScript**: Vanilla JS for interactivity
- **TypeScript**: For type safety and better development experience

## File Structure

```
src/
├── web/
│   └── server.ts          # Express server and API routes
├── views/
│   └── index.ejs          # Main HTML template
├── public/
│   ├── styles.css         # CSS styles
│   └── script.js          # Client-side JavaScript
└── database/
    └── Database.ts        # Database connection (existing)
```

## Performance Features

- **Pagination**: Results are paginated (1000 per page) for better performance
- **Debounced Search**: Search input is debounced to reduce database queries
- **Indexed Queries**: Database queries use existing indexes for fast filtering
- **Responsive Images**: Optimized for different screen sizes

## Browser Compatibility

- **Modern Browsers**: Chrome, Firefox, Safari, Edge (latest versions)
- **Mobile Browsers**: iOS Safari, Chrome Mobile, Samsung Internet
- **Features**: CSS Grid, Flexbox, ES6+ JavaScript

## Customization

### Changing the Port
Set the `PORT` environment variable:
```bash
PORT=8080 npm run web
```

### Modifying Styles
Edit `src/public/styles.css` to customize the appearance.

### Adding Features
The modular structure makes it easy to add new features:
- Add new routes in `src/web/server.ts`
- Modify the template in `src/views/index.ejs`
- Add client-side functionality in `src/public/script.js`

## Troubleshooting

### Common Issues

1. **Port Already in Use**
   - Change the port using the `PORT` environment variable
   - Or kill the process using the port: `lsof -ti:3000 | xargs kill`

2. **Database Not Found**
   - Ensure `clutchscrape.db` exists in the project root
   - Run the original scraping application first to populate the database

3. **TypeScript Compilation Errors**
   - Run `npm run build` to check for compilation issues
   - Ensure all dependencies are installed: `npm install`

### Performance Tips

- For large datasets (>1000 agencies), consider implementing virtual scrolling
- Add database indexes for frequently filtered columns
- Use caching for repeated queries

## Contributing

To add new features or fix bugs:

1. Make changes to the appropriate files
2. Test in development mode: `npm run web`
3. Build and test production version: `npm run web:build`
4. Ensure responsive design works on different screen sizes

## License

This web application is part of the clutch-scrape-ts project and follows the same MIT license.
