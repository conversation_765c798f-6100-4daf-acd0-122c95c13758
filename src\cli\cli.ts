import { Command } from 'commander';
import { promises as fs } from 'fs';
import path from 'path';
import { Database, DEFAULT_DB_PATH } from '../database/Database';
import { FileScanner } from '../scanner/FileScanner';
import { HtmlParser } from '../parser/HtmlParser';
import { Agency } from '../models/Agency';

interface ProcessOptions {
  db: string;
  verbose: boolean;
  inputFolder: string;
  minRating?: number;
  hasWebsite?: boolean;
  export?: string;
}

interface StatsOptions {
  db: string;
  verbose: boolean;
}

export class ClutchScrapeCli {
  private program: Command;

  constructor() {
    this.program = new Command();
    this.setupCommands();
  }

  private setupCommands(): void {
    this.program
      .name('clutch-scrape-ts')
      .description('Process local HTML files to extract agency data and store results in SQLite')
      .version('1.0.0');

    this.program
      .option('--db <path>', `SQLite DB path (default: ${DEFAULT_DB_PATH})`, DEFAULT_DB_PATH)
      .option('--verbose', 'Enable verbose logging', false);

    // Process command (replaces scrape command)
    this.program
      .command('process')
      .description('Process HTML files from input folder and store results in the database')
      .option('--input-folder <path>', 'Input folder containing HTML files', 'input')
      .option('--min-rating <rating>', 'Minimum rating filter (e.g., 4.5)', parseFloat)
      .option('--has-website', 'Only process agencies with websites')
      .option('--export <path>', 'Optional export path (.csv or .json)')
      .action(async (options) => {
        const globalOptions = this.program.opts();
        await this.processCommand({
          ...globalOptions,
          ...options
        });
      });

    // Stats command
    this.program
      .command('stats')
      .description('Show basic statistics about processed data')
      .action(async () => {
        const globalOptions = this.program.opts() as StatsOptions;
        await this.statsCommand(globalOptions);
      });
  }

  private configureLogging(verbose: boolean): void {
    if (verbose) {
      console.log('Verbose logging enabled');
    }
  }

  private async processCommand(options: ProcessOptions): Promise<void> {
    this.configureLogging(options.verbose);

    try {
      // Initialize components
      const scanner = new FileScanner({ 
        inputFolder: options.inputFolder,
        fileExtensions: ['.html', '.htm'],
        recursive: false
      });
      
      const parser = new HtmlParser();
      const database = new Database(options.db);

      // Validate input folder
      if (!(await scanner.validateInputFolder())) {
        console.error(`Input folder '${options.inputFolder}' does not exist`);
        console.log(`Please create the folder and add HTML files to process`);
        process.exit(1);
      }

      // Scan for HTML files
      console.log(`Scanning for HTML files in '${options.inputFolder}'...`);
      const htmlFiles = await scanner.scanForHtmlFiles();

      if (htmlFiles.length === 0) {
        console.log(`No HTML files found in '${options.inputFolder}'`);
        console.log(`Supported extensions: ${scanner.getSupportedExtensions().join(', ')}`);
        return;
      }

      console.log(`Found ${htmlFiles.length} HTML file(s) to process`);

      // Process each file
      const allAgencies: Agency[] = [];
      
      for (const filePath of htmlFiles) {
        console.log(`Processing: ${filePath}`);
        
        try {
          const parseOptions: { minRating?: number } = {};
          if (options.minRating !== undefined) {
            parseOptions.minRating = options.minRating;
          }
          let agencies = await parser.parseFile(filePath, parseOptions);

          // Apply hasWebsite filter if specified
          if (options.hasWebsite) {
            const beforeCount = agencies.length;
            agencies = agencies.filter(agency =>
              agency.contactWebsite &&
              agency.contactWebsite.trim() !== ''
            );
            console.log(`  Filtered to ${agencies.length} agencies with websites (removed ${beforeCount - agencies.length})`);
          } else {
            console.log(`  Extracted ${agencies.length} agencies`);
          }

          allAgencies.push(...agencies);
        } catch (error) {
          console.error(`  Error processing ${filePath}: ${(error as Error).message}`);
        }
      }

      if (allAgencies.length === 0) {
        console.log('No agencies extracted from HTML files');
        return;
      }

      // Store in database
      console.log(`Storing ${allAgencies.length} agencies in database...`);
      const upsertedCount = await database.upsertAgencies(allAgencies);
      console.log(`Upserted ${upsertedCount} agencies into ${options.db}`);

      // Export if requested
      if (options.export) {
        await this.exportData(allAgencies, options.export);
      }

      database.close();
    } catch (error) {
      console.error('Error during processing:', (error as Error).message);
      process.exit(1);
    }
  }

  private async statsCommand(options: StatsOptions): Promise<void> {
    this.configureLogging(options.verbose);

    try {
      const database = new Database(options.db);
      const stats = await database.getStats();
      
      console.log(JSON.stringify(stats, null, 2));
      
      database.close();
    } catch (error) {
      console.error('Error getting stats:', (error as Error).message);
      process.exit(1);
    }
  }

  private async exportData(agencies: Agency[], exportPath: string): Promise<void> {
    const ext = path.extname(exportPath).toLowerCase();
    
    try {
      if (ext === '.json') {
        await fs.writeFile(exportPath, JSON.stringify(agencies, null, 2), 'utf-8');
        console.log(`Exported ${agencies.length} rows to ${exportPath}`);
      } else if (ext === '.csv') {
        await this.exportToCsv(agencies, exportPath);
        console.log(`Exported ${agencies.length} rows to ${exportPath}`);
      } else {
        console.warn(`Unsupported export extension: ${ext} (use .csv or .json)`);
      }
    } catch (error) {
      console.error(`Failed to export to ${exportPath}: ${(error as Error).message}`);
    }
  }

  private async exportToCsv(agencies: Agency[], filePath: string): Promise<void> {
    const headers = [
      'name',
      'rating',
      'review_count',
      'location',
      'description',
      'contact_phone',
      'contact_website',
      'clutch_profile_url'
    ];

    const csvLines = [headers.join(',')];
    
    for (const agency of agencies) {
      const row = [
        this.escapeCsvField(agency.name),
        agency.rating?.toString() || '',
        agency.reviewCount?.toString() || '',
        this.escapeCsvField(agency.location),
        this.escapeCsvField(agency.description),
        this.escapeCsvField(agency.contactPhone),
        this.escapeCsvField(agency.contactWebsite),
        this.escapeCsvField(agency.clutchProfileUrl)
      ];
      csvLines.push(row.join(','));
    }

    await fs.writeFile(filePath, csvLines.join('\n'), 'utf-8');
  }

  private escapeCsvField(value: string | null): string {
    if (value === null) {
      return '';
    }
    
    // Escape quotes and wrap in quotes if contains comma, quote, or newline
    if (value.includes(',') || value.includes('"') || value.includes('\n')) {
      return `"${value.replace(/"/g, '""')}"`;
    }
    
    return value;
  }

  async run(argv?: string[]): Promise<void> {
    await this.program.parseAsync(argv);
  }
}
