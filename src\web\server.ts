const express = require('express');
import * as path from 'path';
import { Request, Response } from 'express';
import { Database } from '../database/Database';
import { Agency } from '../models/Agency';
import { DuplicateDetectionService, DuplicateDetectionCriteria, AgencyWithId } from '../services/DuplicateDetectionService';
import { BackupService, DuplicateAction } from '../services/BackupService';

const app = express();
const PORT = process.env.PORT || 3000;

// Initialize database
const db = new Database();

// Set up view engine
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, '../views'));

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, '../public')));

// Interface for search/filter parameters
interface SearchParams {
  search?: string | undefined;
  location?: string | undefined;
  minRating?: number | undefined;
  maxRating?: number | undefined;
  minReviews?: number | undefined;
  maxReviews?: number | undefined;
  hasWebsite?: boolean | undefined;
  page?: number | undefined;
  limit?: number | undefined;
}

// Helper function to build SQL query with filters
function buildSearchQuery(params: SearchParams): { query: string; values: any[] } {
  let query = 'SELECT * FROM agencies WHERE 1=1';
  const values: any[] = [];

  if (params.search) {
    query += ` AND (
      name LIKE ? OR 
      description LIKE ? OR 
      location LIKE ? OR 
      contact_website LIKE ?
    )`;
    const searchTerm = `%${params.search}%`;
    values.push(searchTerm, searchTerm, searchTerm, searchTerm);
  }

  if (params.location) {
    query += ' AND location LIKE ?';
    values.push(`%${params.location}%`);
  }

  if (params.minRating !== undefined) {
    query += ' AND rating >= ?';
    values.push(params.minRating);
  }

  if (params.maxRating !== undefined) {
    query += ' AND rating <= ?';
    values.push(params.maxRating);
  }

  if (params.minReviews !== undefined) {
    query += ' AND review_count >= ?';
    values.push(params.minReviews);
  }

  if (params.maxReviews !== undefined) {
    query += ' AND review_count <= ?';
    values.push(params.maxReviews);
  }

  query += ' ORDER BY rating DESC, review_count DESC';

  // Add pagination
  const limit = params.limit || 1000;
  const offset = ((params.page || 1) - 1) * limit;
  query += ' LIMIT ? OFFSET ?';
  values.push(limit, offset);

  return { query, values };
}

// Helper function to get total count for pagination
function buildCountQuery(params: SearchParams): { query: string; values: any[] } {
  let query = 'SELECT COUNT(*) as total FROM agencies WHERE 1=1';
  const values: any[] = [];

  if (params.search) {
    query += ` AND (
      name LIKE ? OR 
      description LIKE ? OR 
      location LIKE ? OR 
      contact_website LIKE ?
    )`;
    const searchTerm = `%${params.search}%`;
    values.push(searchTerm, searchTerm, searchTerm, searchTerm);
  }

  if (params.location) {
    query += ' AND location LIKE ?';
    values.push(`%${params.location}%`);
  }

  if (params.minRating !== undefined) {
    query += ' AND rating >= ?';
    values.push(params.minRating);
  }

  if (params.maxRating !== undefined) {
    query += ' AND rating <= ?';
    values.push(params.maxRating);
  }

  if (params.minReviews !== undefined) {
    query += ' AND review_count >= ?';
    values.push(params.minReviews);
  }

  if (params.maxReviews !== undefined) {
    query += ' AND review_count <= ?';
    values.push(params.maxReviews);
  }

  if (params.hasWebsite === true) {
    query += ' AND contact_website IS NOT NULL AND TRIM(contact_website) <> ""';
  }

  return { query, values };
}

// Routes
app.get('/', async (req: Request, res: Response) => {
  try {
    const params: SearchParams = {
      search: req.query.search as string,
      location: req.query.location as string,
      minRating: req.query.minRating ? parseFloat(req.query.minRating as string) : undefined,
      maxRating: req.query.maxRating ? parseFloat(req.query.maxRating as string) : undefined,
      minReviews: req.query.minReviews ? parseInt(req.query.minReviews as string) : undefined,
      maxReviews: req.query.maxReviews ? parseInt(req.query.maxReviews as string) : undefined,
      hasWebsite: req.query.hasWebsite === 'true',
      page: req.query.page ? parseInt(req.query.page as string) : 1,
      limit: 1000
    };

    const { query, values } = buildSearchQuery(params);
    const { query: countQuery, values: countValues } = buildCountQuery(params);

    // Get agencies and total count
    const agencies = await new Promise<any[]>((resolve, reject) => {
      (db as any).db.all(query, values, (err: any, rows: any[]) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    const totalResult = await new Promise<{ total: number }>((resolve, reject) => {
      (db as any).db.get(countQuery, countValues, (err: any, row: any) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    // Get unique locations for filter dropdown
    const locations = await new Promise<string[]>((resolve, reject) => {
      (db as any).db.all(
        'SELECT DISTINCT location FROM agencies WHERE location IS NOT NULL AND TRIM(location) <> "" ORDER BY location',
        (err: any, rows: any[]) => {
          if (err) reject(err);
          else resolve(rows.map((row: any) => row.location));
        }
      );
    });

    const totalPages = Math.ceil(totalResult.total / params.limit!);

    res.render('index', {
      agencies,
      locations,
      params,
      totalPages,
      currentPage: params.page,
      totalResults: totalResult.total
    });
  } catch (error) {
    console.error('Error fetching agencies:', error);
    res.status(500).send('Internal Server Error');
  }
});

// API endpoint for AJAX requests
app.get('/api/agencies', async (req: Request, res: Response) => {
  try {
    const params: SearchParams = {
      search: req.query.search as string,
      location: req.query.location as string,
      minRating: req.query.minRating ? parseFloat(req.query.minRating as string) : undefined,
      maxRating: req.query.maxRating ? parseFloat(req.query.maxRating as string) : undefined,
      minReviews: req.query.minReviews ? parseInt(req.query.minReviews as string) : undefined,
      maxReviews: req.query.maxReviews ? parseInt(req.query.maxReviews as string) : undefined,
      hasWebsite: req.query.hasWebsite === 'true',
      page: req.query.page ? parseInt(req.query.page as string) : 1,
      limit: 1000
    };

    const { query, values } = buildSearchQuery(params);
    const { query: countQuery, values: countValues } = buildCountQuery(params);

    const agencies = await new Promise<any[]>((resolve, reject) => {
      (db as any).db.all(query, values, (err: any, rows: any[]) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    const totalResult = await new Promise<{ total: number }>((resolve, reject) => {
      (db as any).db.get(countQuery, countValues, (err: any, row: any) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    const totalPages = Math.ceil(totalResult.total / params.limit!);

    res.json({
      agencies,
      totalPages,
      currentPage: params.page,
      totalResults: totalResult.total
    });
  } catch (error) {
    console.error('Error fetching agencies:', error);
    res.status(500).json({ error: 'Internal Server Error' });
  }
});

// Duplicate Management Routes

// Get duplicate management page
app.get('/duplicates', (req: Request, res: Response) => {
  res.render('duplicates', {
    title: 'Duplicate Management'
  });
});

// API: Detect duplicates
app.post('/api/duplicates/detect', async (req: Request, res: Response) => {
  try {
    const criteria: DuplicateDetectionCriteria = {
      exactNameMatch: req.body.exactNameMatch === true,
      fuzzyNameMatch: req.body.fuzzyNameMatch === true,
      fuzzyNameThreshold: parseInt(req.body.fuzzyNameThreshold) || 85,
      sameWebsite: req.body.sameWebsite === true,
      sameClutchProfile: req.body.sameClutchProfile === true,
      locationAndSimilarName: req.body.locationAndSimilarName === true,
      locationNameThreshold: parseInt(req.body.locationNameThreshold) || 80
    };

    // Get all agencies from database
    const agencies = await new Promise<AgencyWithId[]>((resolve, reject) => {
      (db as any).db.all('SELECT * FROM agencies ORDER BY name', (err: any, rows: any[]) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    const result = DuplicateDetectionService.detectDuplicates(agencies, criteria);
    res.json(result);
  } catch (error) {
    console.error('Error detecting duplicates:', error);
    res.status(500).json({ error: 'Failed to detect duplicates' });
  }
});

// API: Create backup
app.post('/api/backups/create', async (req: Request, res: Response) => {
  try {
    const description = req.body.description || 'Manual backup before duplicate processing';
    const backup = await BackupService.createBackup(description);
    res.json(backup);
  } catch (error) {
    console.error('Error creating backup:', error);
    res.status(500).json({ error: 'Failed to create backup' });
  }
});

// API: List backups
app.get('/api/backups', async (req: Request, res: Response) => {
  try {
    const backups = await BackupService.listBackups();
    res.json(backups);
  } catch (error) {
    console.error('Error listing backups:', error);
    res.status(500).json({ error: 'Failed to list backups' });
  }
});

// API: Restore backup
app.post('/api/backups/restore/:backupId', async (req: Request, res: Response) => {
  try {
    const backupId = req.params.backupId;
    if (!backupId) {
      return res.status(400).json({ error: 'Backup ID is required' });
    }
    await BackupService.restoreBackup(backupId);
    return res.json({ success: true, message: 'Backup restored successfully' });
  } catch (error) {
    console.error('Error restoring backup:', error);
    return res.status(500).json({ error: 'Failed to restore backup' });
  }
});

// API: Process duplicates
app.post('/api/duplicates/process', async (req: Request, res: Response) => {
  try {
    const { actions } = req.body; // Array of { groupId, action, selectedAgencyId? }

    if (!Array.isArray(actions)) {
      return res.status(400).json({ error: 'Actions must be an array' });
    }

    const results = [];

    for (const actionItem of actions) {
      const { groupId, action, selectedAgencyId, agencies } = actionItem;

      if (!groupId || !action || !Array.isArray(agencies)) {
        results.push({ groupId, success: false, error: 'Invalid action data' });
        continue;
      }

      try {
        let keptAgencyId: number | undefined;
        const agencyIds = agencies.map((a: any) => a.id);

        if (action === 'keep_best') {
          const bestAgency = DuplicateDetectionService.getBestAgency(agencies);
          keptAgencyId = bestAgency.id;

          // Delete all others
          const toDelete = agencyIds.filter(id => id !== keptAgencyId);
          await DatabaseHelpers.deleteAgencies(toDelete);

        } else if (action === 'keep_selected') {
          if (!selectedAgencyId) {
            results.push({ groupId, success: false, error: 'No agency selected' });
            continue;
          }

          keptAgencyId = selectedAgencyId;
          const toDelete = agencyIds.filter(id => id !== selectedAgencyId);
          await DatabaseHelpers.deleteAgencies(toDelete);

        } else if (action === 'merge') {
          const mergedData = DuplicateDetectionService.mergeAgencies(agencies);
          keptAgencyId = mergedData.id;

          // Update the best agency with merged data
          await DatabaseHelpers.updateAgency(mergedData);

          // Delete all others
          const toDelete = agencyIds.filter(id => id !== keptAgencyId);
          await DatabaseHelpers.deleteAgencies(toDelete);

        } else if (action === 'skip') {
          // Do nothing
          results.push({ groupId, success: true, message: 'Group skipped' });
          continue;
        }

        // Log the action
        await BackupService.logAction({
          id: `action_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`,
          timestamp: new Date(),
          action: action === 'skip' ? 'keep' : 'delete',
          groupId,
          agenciesAffected: agencyIds,
          keptAgencyId,
          description: `${action} action on duplicate group ${groupId}`
        });

        results.push({ groupId, success: true, keptAgencyId });

      } catch (error) {
        console.error(`Error processing group ${groupId}:`, error);
        results.push({ groupId, success: false, error: (error as Error).message });
      }
    }

    return res.json({ results });
  } catch (error) {
    console.error('Error processing duplicates:', error);
    return res.status(500).json({ error: 'Failed to process duplicates' });
  }
});

// Helper functions
const DatabaseHelpers = {
  async deleteAgencies(agencyIds: number[]): Promise<void> {
    if (agencyIds.length === 0) return;

    const placeholders = agencyIds.map(() => '?').join(',');
    const query = `DELETE FROM agencies WHERE id IN (${placeholders})`;

    return new Promise((resolve, reject) => {
      (db as any).db.run(query, agencyIds, (err: any) => {
        if (err) reject(err);
        else resolve();
      });
    });
  },

  async updateAgency(agencyData: Partial<AgencyWithId>): Promise<void> {
    const query = `
      UPDATE agencies SET
        name = ?, rating = ?, review_count = ?, location = ?, description = ?,
        contact_phone = ?, contact_website = ?, clutch_profile_url = ?,
        data_json = ?, scraped_at = ?
      WHERE id = ?
    `;

    return new Promise((resolve, reject) => {
      (db as any).db.run(query, [
        agencyData.name,
        agencyData.rating,
        agencyData.reviewCount,
        agencyData.location,
        agencyData.description,
        agencyData.contactPhone,
        agencyData.contactWebsite,
        agencyData.clutchProfileUrl,
        JSON.stringify(agencyData.dataJson),
        agencyData.scraped_at,
        agencyData.id
      ], (err: any) => {
        if (err) reject(err);
        else resolve();
      });
    });
  }
};

// API: Get action log
app.get('/api/duplicates/log', async (req: Request, res: Response) => {
  try {
    const log = await BackupService.getActionLog();
    res.json(log);
  } catch (error) {
    console.error('Error getting action log:', error);
    res.status(500).json({ error: 'Failed to get action log' });
  }
});

// Statistics page route
app.get('/stats', async (req: Request, res: Response) => {
  try {
    const stats = await db.getStats();
    res.render('stats', { stats });
  } catch (error) {
    console.error('Error fetching statistics:', error);
    res.status(500).send('Internal Server Error');
  }
});

// Start server
app.listen(PORT, () => {
  console.log(`Server running on http://localhost:${PORT}`);
});

export default app;
