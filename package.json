{"name": "clutch-scrape-ts", "version": "1.0.0", "description": "TypeScript application for processing local HTML files to extract agency data", "main": "dist/main.js", "scripts": {"build": "tsc && node scripts/copy-static.js", "start": "node dist/main.js", "dev": "ts-node src/main.ts", "web": "ts-node src/web/server.ts", "web:build": "npm run build && node dist/web/server.js", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean", "reset-db": "node -e \"const fs = require('fs'); const dbPath = 'clutchscrape.db'; if (fs.existsSync(dbPath)) { fs.unlinkSync(dbPath); console.log('Database cleared.'); } else { console.log('Database file not found, nothing to clear.'); }\" && npm run dev -- process --input-folder input"}, "keywords": ["scraping", "html", "typescript", "clutch"], "author": "", "license": "MIT", "devDependencies": {"@types/ejs": "^3.1.5", "@types/express": "^5.0.3", "@types/node": "^20.0.0", "@types/sqlite3": "^3.1.11", "rimraf": "^5.0.0", "ts-node": "^10.9.0", "typescript": "^5.0.0"}, "dependencies": {"cheerio": "^1.0.0-rc.12", "commander": "^11.0.0", "cors": "^2.8.5", "ejs": "^3.1.10", "express": "^5.1.0", "fuzzball": "^2.2.3", "sqlite3": "^5.1.6"}}