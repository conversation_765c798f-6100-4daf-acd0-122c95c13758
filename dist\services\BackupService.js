"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.BackupService = void 0;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const Database_1 = require("../database/Database");
class BackupService {
    static async ensureBackupDirectory() {
        if (!fs.existsSync(this.BACKUP_DIR)) {
            fs.mkdirSync(this.BACKUP_DIR, { recursive: true });
        }
    }
    static async createBackup(description = 'Manual backup') {
        await this.ensureBackupDirectory();
        const timestamp = new Date();
        const timestampStr = timestamp.toISOString().replace(/[:.]/g, '-').split('T')[0] + '_' +
            (timestamp.toISOString().split('T')[1]?.split('.')[0]?.replace(/:/g, '-') || '');
        const backupId = `backup_${timestampStr}`;
        const filename = `${backupId}.db`;
        const backupPath = path.join(this.BACKUP_DIR, filename);
        const originalDbPath = 'clutchscrape.db';
        if (!fs.existsSync(originalDbPath)) {
            throw new Error('Original database file not found');
        }
        fs.copyFileSync(originalDbPath, backupPath);
        const stats = fs.statSync(backupPath);
        const backupInfo = {
            id: backupId,
            filename,
            timestamp,
            size: stats.size,
            description
        };
        await this.logAction({
            id: `backup_${Date.now()}`,
            timestamp,
            action: 'keep',
            groupId: 'backup',
            agenciesAffected: [],
            description: `Backup created: ${filename} - ${description}`
        });
        return backupInfo;
    }
    static async listBackups() {
        await this.ensureBackupDirectory();
        const backups = [];
        const files = fs.readdirSync(this.BACKUP_DIR);
        for (const file of files) {
            if (file.endsWith('.db')) {
                const filePath = path.join(this.BACKUP_DIR, file);
                const stats = fs.statSync(filePath);
                const timestampMatch = file.match(/backup_(\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2})/);
                let timestamp = stats.mtime;
                if (timestampMatch && timestampMatch[1]) {
                    const timestampStr = timestampMatch[1].replace(/_/g, 'T').replace(/-/g, ':');
                    timestamp = new Date(timestampStr + '.000Z');
                }
                backups.push({
                    id: file.replace('.db', ''),
                    filename: file,
                    timestamp,
                    size: stats.size,
                    description: 'Database backup'
                });
            }
        }
        return backups.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
    }
    static async restoreBackup(backupId) {
        const backupPath = path.join(this.BACKUP_DIR, `${backupId}.db`);
        if (!fs.existsSync(backupPath)) {
            throw new Error(`Backup file not found: ${backupId}`);
        }
        await this.createBackup('Pre-restore backup');
        const originalDbPath = 'clutchscrape.db';
        fs.copyFileSync(backupPath, originalDbPath);
        await this.logAction({
            id: `restore_${Date.now()}`,
            timestamp: new Date(),
            action: 'keep',
            groupId: 'restore',
            agenciesAffected: [],
            description: `Database restored from backup: ${backupId}`
        });
    }
    static async logAction(action) {
        await this.ensureBackupDirectory();
        const logPath = path.join(this.BACKUP_DIR, this.LOG_FILE);
        const logEntry = JSON.stringify(action) + '\n';
        fs.appendFileSync(logPath, logEntry);
    }
    static async getActionLog() {
        const logPath = path.join(this.BACKUP_DIR, this.LOG_FILE);
        if (!fs.existsSync(logPath)) {
            return [];
        }
        const logContent = fs.readFileSync(logPath, 'utf-8');
        const lines = logContent.trim().split('\n').filter(line => line.length > 0);
        return lines.map(line => {
            try {
                const action = JSON.parse(line);
                action.timestamp = new Date(action.timestamp);
                return action;
            }
            catch (error) {
                console.warn('Failed to parse log entry:', line);
                return null;
            }
        }).filter(action => action !== null);
    }
    static async cleanupOldBackups(keepCount = 10) {
        const backups = await this.listBackups();
        if (backups.length <= keepCount) {
            return;
        }
        const toDelete = backups.slice(keepCount);
        for (const backup of toDelete) {
            const backupPath = path.join(this.BACKUP_DIR, backup.filename);
            if (fs.existsSync(backupPath)) {
                fs.unlinkSync(backupPath);
                console.log(`Deleted old backup: ${backup.filename}`);
            }
        }
    }
    static formatFileSize(bytes) {
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        if (bytes === 0)
            return '0 Bytes';
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    }
    static async getDatabaseStats() {
        const dbPath = 'clutchscrape.db';
        if (!fs.existsSync(dbPath)) {
            return { size: 0, agencyCount: 0 };
        }
        const stats = fs.statSync(dbPath);
        const db = new Database_1.Database();
        const agencyCount = await new Promise((resolve, reject) => {
            db.db.get('SELECT COUNT(*) as count FROM agencies', (err, row) => {
                if (err) {
                    reject(err);
                }
                else {
                    resolve(row.count);
                }
            });
        });
        db.close();
        return {
            size: stats.size,
            agencyCount
        };
    }
}
exports.BackupService = BackupService;
BackupService.BACKUP_DIR = 'backups';
BackupService.LOG_FILE = 'duplicate_actions.log';
//# sourceMappingURL=BackupService.js.map