<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clutch Agencies Explorer</title>
    <link rel="stylesheet" href="/styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🔍 Clutch Agencies Explorer</h1>
            <p>Explore <%= totalResults %> recruiting agencies from Clutch.co</p>
            <nav class="header-nav">
                <a href="/stats" class="nav-link">📊 Statistics</a>
                <a href="/duplicates" class="nav-link">🔧 Manage Duplicates</a>
            </nav>
        </header>

        <!-- Search and Filter Form -->
        <div class="filters-section">
            <form id="searchForm" method="GET">
                <div class="search-row">
                    <div class="search-field">
                        <label for="search">Search:</label>
                        <input type="text" id="search" name="search" placeholder="Search name, description, location..." value="<%= params.search || '' %>">
                    </div>
                    <div class="search-field">
                        <label for="location">Location:</label>
                        <select id="location" name="location">
                            <option value="">All Locations</option>
                            <% locations.forEach(loc => { %>
                                <option value="<%= loc %>" <%= params.location === loc ? 'selected' : '' %>><%= loc %></option>
                            <% }); %>
                        </select>
                    </div>
                </div>

                <div class="filter-row">
                    <div class="filter-group">
                        <label>Rating:</label>
                        <input type="number" name="minRating" placeholder="Min" step="0.1" min="0" max="5" value="<%= params.minRating || '' %>">
                        <span>to</span>
                        <input type="number" name="maxRating" placeholder="Max" step="0.1" min="0" max="5" value="<%= params.maxRating || '' %>">
                    </div>
                    <div class="filter-group">
                        <label>Reviews:</label>
                        <input type="number" name="minReviews" placeholder="Min" min="0" value="<%= params.minReviews || '' %>">
                        <span>to</span>
                        <input type="number" name="maxReviews" placeholder="Max" min="0" value="<%= params.maxReviews || '' %>">
                    </div>
                </div>

                <div class="filter-row">
                    <div class="filter-group">
                        <label>
                            <input type="checkbox" name="hasWebsite" value="true" <%= params.hasWebsite ? 'checked' : '' %>>
                            Only show agencies with websites
                        </label>
                    </div>
                </div>

                <div class="button-row">
                    <button type="submit">Search</button>
                    <button type="button" id="clearFilters">Clear Filters</button>
                </div>
            </form>
        </div>

        <!-- Results Summary -->
        <div class="results-summary">
            <p>Showing <%= agencies.length %> of <%= totalResults %> agencies</p>
            <div class="view-toggle">
                <button id="tableView" class="view-btn active">Table View</button>
                <button id="cardView" class="view-btn">Card View</button>
            </div>
        </div>

        <!-- Loading indicator -->
        <div id="loading" class="loading" style="display: none;">
            <p>Loading...</p>
        </div>

        <!-- Results Container -->
        <div id="resultsContainer">
            <!-- Table View -->
            <div id="tableViewContainer" class="table-container">
                <table class="agencies-table">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Rating</th>
                            <th>Reviews</th>
                            <th>Location</th>
                            <th>Description</th>
                            <th>Website</th>
                        </tr>
                    </thead>
                    <tbody>
                        <% agencies.forEach(agency => { %>
                            <tr>
                                <td class="agency-name" data-label="Name">
                                    <% if (agency.clutch_profile_url) { %>
                                        <a href="<%= agency.clutch_profile_url %>" target="_blank"><%= agency.name || 'N/A' %></a>
                                    <% } else { %>
                                        <%= agency.name || 'N/A' %>
                                    <% } %>
                                </td>
                                <td class="rating" data-label="Rating">
                                    <% if (agency.rating) { %>
                                        <span class="rating-value">⭐ <%= agency.rating %></span>
                                    <% } else { %>
                                        N/A
                                    <% } %>
                                </td>
                                <td class="review-count" data-label="Reviews"><%= agency.review_count || 0 %></td>
                                <td class="location" data-label="Location"><%= agency.location || 'N/A' %></td>
                                <td class="description" data-label="Description">
                                    <div class="description-text">
                                        <%= (agency.description || 'No description available').substring(0, 150) %>
                                        <% if (agency.description && agency.description.length > 150) { %>...
                                            <button class="expand-btn" onclick="toggleDescription(this)">Show More</button>
                                            <div class="full-description" style="display: none;"><%= agency.description %></div>
                                        <% } %>
                                    </div>
                                </td>
                                <td class="website" data-label="Website">
                                    <% if (agency.contact_website) { %>
                                        <a href="<%= agency.contact_website %>" target="_blank">Visit Site</a>
                                    <% } else { %>
                                        N/A
                                    <% } %>
                                </td>
                            </tr>
                        <% }); %>
                    </tbody>
                </table>
            </div>

            <!-- Card View -->
            <div id="cardViewContainer" class="cards-container" style="display: none;">
                <% agencies.forEach(agency => { %>
                    <div class="agency-card">
                        <div class="card-header">
                            <h3 class="agency-name">
                                <% if (agency.clutch_profile_url) { %>
                                    <a href="<%= agency.clutch_profile_url %>" target="_blank"><%= agency.name || 'N/A' %></a>
                                <% } else { %>
                                    <%= agency.name || 'N/A' %>
                                <% } %>
                            </h3>
                            <div class="rating-info">
                                <% if (agency.rating) { %>
                                    <span class="rating">⭐ <%= agency.rating %></span>
                                <% } %>
                                <span class="reviews">(<%= agency.review_count || 0 %> reviews)</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <p class="location">📍 <%= agency.location || 'Location not specified' %></p>
                            <p class="description"><%= agency.description || 'No description available' %></p>
                            <% if (agency.contact_website) { %>
                                <p class="website">
                                    <a href="<%= agency.contact_website %>" target="_blank">🌐 Visit Website</a>
                                </p>
                            <% } %>
                        </div>
                    </div>
                <% }); %>
            </div>
        </div>

        <!-- Pagination -->
        <% if (totalPages > 1) { %>
            <div class="pagination">
                <% if (currentPage > 1) { %>
                    <a href="?<%= new URLSearchParams({...params, page: currentPage - 1}).toString() %>" class="page-btn">← Previous</a>
                <% } %>
                
                <% for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) { %>
                    <% if (i === currentPage) { %>
                        <span class="page-btn current"><%= i %></span>
                    <% } else { %>
                        <a href="?<%= new URLSearchParams({...params, page: i}).toString() %>" class="page-btn"><%= i %></a>
                    <% } %>
                <% } %>
                
                <% if (currentPage < totalPages) { %>
                    <a href="?<%= new URLSearchParams({...params, page: currentPage + 1}).toString() %>" class="page-btn">Next →</a>
                <% } %>
            </div>
        <% } %>
    </div>

    <script src="/script.js"></script>
</body>
</html>
