// Duplicate Management JavaScript

// Global variables
let duplicateResults = null;
let groupActions = new Map(); // groupId -> { action, selectedAgencyId }

// DOM elements
const detectBtn = document.getElementById('detectBtn');
const resetCriteriaBtn = document.getElementById('resetCriteriaBtn');
const loadingIndicator = document.getElementById('loadingIndicator');
const resultsSection = document.getElementById('resultsSection');
const resultsSummary = document.getElementById('resultsSummary');
const duplicateGroups = document.getElementById('duplicateGroups');
const createBackupBtn = document.getElementById('createBackupBtn');
const viewBackupsBtn = document.getElementById('viewBackupsBtn');
const backupStatus = document.getElementById('backupStatus');
const processBtn = document.getElementById('processBtn');
const processStatus = document.getElementById('processStatus');
const applyBulkBtn = document.getElementById('applyBulkBtn');
const bulkAction = document.getElementById('bulkAction');

// Threshold sliders
const fuzzyNameThreshold = document.getElementById('fuzzyNameThreshold');
const fuzzyNameValue = document.getElementById('fuzzyNameValue');
const locationNameThreshold = document.getElementById('locationNameThreshold');
const locationNameValue = document.getElementById('locationNameValue');

// Initialize
document.addEventListener('DOMContentLoaded', () => {
    setupEventListeners();
    updateThresholdValues();
});

function setupEventListeners() {
    detectBtn.addEventListener('click', detectDuplicates);
    resetCriteriaBtn.addEventListener('click', resetCriteria);
    createBackupBtn.addEventListener('click', createBackup);
    viewBackupsBtn.addEventListener('click', showBackupModal);
    processBtn.addEventListener('click', processDuplicates);
    applyBulkBtn.addEventListener('click', applyBulkAction);
    
    // Threshold sliders
    fuzzyNameThreshold.addEventListener('input', updateThresholdValues);
    locationNameThreshold.addEventListener('input', updateThresholdValues);
    
    // Bulk action dropdown
    bulkAction.addEventListener('change', () => {
        applyBulkBtn.disabled = !bulkAction.value;
    });
    
    // Modal close handlers
    document.getElementById('confirmNo').addEventListener('click', closeConfirmModal);
}

function updateThresholdValues() {
    fuzzyNameValue.textContent = fuzzyNameThreshold.value + '%';
    locationNameValue.textContent = locationNameThreshold.value + '%';
}

function resetCriteria() {
    document.getElementById('exactNameMatch').checked = true;
    document.getElementById('fuzzyNameMatch').checked = true;
    document.getElementById('fuzzyNameThreshold').value = 85;
    document.getElementById('sameWebsite').checked = true;
    document.getElementById('sameClutchProfile').checked = true;
    document.getElementById('locationAndSimilarName').checked = true;
    document.getElementById('locationNameThreshold').value = 80;
    updateThresholdValues();
}

async function detectDuplicates() {
    try {
        // Show loading
        loadingIndicator.style.display = 'block';
        resultsSection.style.display = 'none';
        
        // Collect criteria
        const criteria = {
            exactNameMatch: document.getElementById('exactNameMatch').checked,
            fuzzyNameMatch: document.getElementById('fuzzyNameMatch').checked,
            fuzzyNameThreshold: parseInt(fuzzyNameThreshold.value),
            sameWebsite: document.getElementById('sameWebsite').checked,
            sameClutchProfile: document.getElementById('sameClutchProfile').checked,
            locationAndSimilarName: document.getElementById('locationAndSimilarName').checked,
            locationNameThreshold: parseInt(locationNameThreshold.value)
        };
        
        // Send request
        const response = await fetch('/api/duplicates/detect', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(criteria)
        });
        
        if (!response.ok) {
            throw new Error('Failed to detect duplicates');
        }
        
        duplicateResults = await response.json();
        
        // Hide loading and show results
        loadingIndicator.style.display = 'none';
        displayResults();
        
    } catch (error) {
        console.error('Error detecting duplicates:', error);
        loadingIndicator.style.display = 'none';
        showStatus(processStatus, 'Error detecting duplicates: ' + error.message, 'error');
    }
}

function displayResults() {
    if (!duplicateResults) return;
    
    // Update summary
    resultsSummary.textContent = 
        `Found ${duplicateResults.totalGroups} duplicate groups with ${duplicateResults.totalDuplicates} total agencies`;
    
    // Clear previous results
    duplicateGroups.innerHTML = '';
    groupActions.clear();
    
    if (duplicateResults.groups.length === 0) {
        duplicateGroups.innerHTML = '<p class="no-duplicates">No duplicates found with the current criteria.</p>';
        resultsSection.style.display = 'block';
        return;
    }
    
    // Create group elements
    duplicateResults.groups.forEach(group => {
        const groupElement = createGroupElement(group);
        duplicateGroups.appendChild(groupElement);
    });
    
    resultsSection.style.display = 'block';
    updateProcessButton();
}

function createGroupElement(group) {
    const groupDiv = document.createElement('div');
    groupDiv.className = 'duplicate-group';
    groupDiv.innerHTML = `
        <div class="group-header" onclick="toggleGroup('${group.id}')">
            <div class="group-info">
                <strong>${group.agencies.length} duplicates</strong>
                <span class="confidence-badge ${getConfidenceClass(group.confidence)}">${group.confidence}% confidence</span>
                <span class="reason">${group.reason}</span>
            </div>
            <span class="expand-icon" id="icon-${group.id}">▼</span>
        </div>
        <div class="group-content" id="content-${group.id}">
            <div class="agencies-comparison">
                ${group.agencies.map(agency => createAgencyCard(agency, group.id)).join('')}
            </div>
            <div class="group-actions">
                <button class="action-btn keep-best-btn" onclick="setGroupAction('${group.id}', 'keep_best')">
                    ⭐ Keep Best
                </button>
                <button class="action-btn keep-selected-btn" onclick="setGroupAction('${group.id}', 'keep_selected')" disabled>
                    ✓ Keep Selected
                </button>
                <button class="action-btn merge-btn" onclick="setGroupAction('${group.id}', 'merge')">
                    🔄 Merge Data
                </button>
                <button class="action-btn skip-btn" onclick="setGroupAction('${group.id}', 'skip')">
                    ⏭️ Skip Group
                </button>
            </div>
            <div class="action-status" id="status-${group.id}"></div>
        </div>
    `;
    
    return groupDiv;
}

function createAgencyCard(agency, groupId) {
    return `
        <div class="agency-card-compare" id="card-${agency.id}">
            <input type="radio" name="select-${groupId}" value="${agency.id}" 
                   class="select-radio" onchange="onAgencySelected('${groupId}', ${agency.id})">
            <div class="card-header">
                <div class="agency-name">${agency.name || 'N/A'}</div>
                <div class="rating-reviews">
                    ${agency.rating ? '⭐ ' + agency.rating : 'No rating'} 
                    (${agency.review_count || 0} reviews)
                </div>
            </div>
            <div class="details">
                <div><strong>ID:</strong> ${agency.id}</div>
                <div><strong>Location:</strong> ${agency.location || 'N/A'}</div>
                <div><strong>Website:</strong> ${agency.contact_website ? 
                    `<a href="${agency.contact_website}" target="_blank">Link</a>` : 'N/A'}</div>
                <div><strong>Scraped:</strong> ${new Date(agency.scraped_at).toLocaleDateString()}</div>
                <div><strong>Description:</strong> ${(agency.description || 'N/A').substring(0, 100)}${
                    agency.description && agency.description.length > 100 ? '...' : ''}</div>
            </div>
        </div>
    `;
}

function getConfidenceClass(confidence) {
    if (confidence >= 90) return 'high';
    if (confidence >= 70) return 'medium';
    return 'low';
}

function toggleGroup(groupId) {
    const content = document.getElementById(`content-${groupId}`);
    const icon = document.getElementById(`icon-${groupId}`);
    
    if (content.classList.contains('expanded')) {
        content.classList.remove('expanded');
        icon.classList.remove('expanded');
    } else {
        content.classList.add('expanded');
        icon.classList.add('expanded');
    }
}

function onAgencySelected(groupId, agencyId) {
    // Update visual selection
    const cards = document.querySelectorAll(`input[name="select-${groupId}"]`);
    cards.forEach(radio => {
        const card = document.getElementById(`card-${radio.value}`);
        if (radio.checked) {
            card.classList.add('selected');
        } else {
            card.classList.remove('selected');
        }
    });
    
    // Enable "Keep Selected" button
    const keepSelectedBtn = document.querySelector(`#content-${groupId} .keep-selected-btn`);
    keepSelectedBtn.disabled = false;
    
    // Update group action if "keep_selected" was already chosen
    if (groupActions.has(groupId) && groupActions.get(groupId).action === 'keep_selected') {
        groupActions.set(groupId, { action: 'keep_selected', selectedAgencyId: agencyId });
    }
}

function setGroupAction(groupId, action) {
    const selectedRadio = document.querySelector(`input[name="select-${groupId}"]:checked`);
    const selectedAgencyId = selectedRadio ? parseInt(selectedRadio.value) : null;
    
    if (action === 'keep_selected' && !selectedAgencyId) {
        showStatus(document.getElementById(`status-${groupId}`), 
                  'Please select an agency first', 'error');
        return;
    }
    
    groupActions.set(groupId, { action, selectedAgencyId });
    
    // Update visual feedback
    const statusDiv = document.getElementById(`status-${groupId}`);
    let message = '';
    switch (action) {
        case 'keep_best':
            message = 'Will keep the agency with highest rating/reviews';
            break;
        case 'keep_selected':
            message = `Will keep selected agency (ID: ${selectedAgencyId})`;
            break;
        case 'merge':
            message = 'Will merge data from all agencies';
            break;
        case 'skip':
            message = 'Will skip this group (no changes)';
            break;
    }
    
    showStatus(statusDiv, message, 'info');
    updateProcessButton();
}

function applyBulkAction() {
    const action = bulkAction.value;
    if (!action || !duplicateResults) return;
    
    duplicateResults.groups.forEach(group => {
        setGroupAction(group.id, action);
    });
    
    showStatus(processStatus, `Applied "${action}" to all ${duplicateResults.groups.length} groups`, 'success');
}

function updateProcessButton() {
    const hasActions = groupActions.size > 0;
    processBtn.disabled = !hasActions;
    
    if (hasActions) {
        const actionCount = Array.from(groupActions.values()).filter(a => a.action !== 'skip').length;
        processBtn.textContent = `🚀 Process ${actionCount} Actions`;
    } else {
        processBtn.textContent = '🚀 Process Selected Actions';
    }
}

async function createBackup() {
    try {
        createBackupBtn.disabled = true;
        const description = document.getElementById('backupDescription').value || 'Manual backup';
        
        const response = await fetch('/api/backups/create', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ description })
        });
        
        if (!response.ok) {
            throw new Error('Failed to create backup');
        }
        
        const backup = await response.json();
        showStatus(backupStatus, 
                  `Backup created successfully: ${backup.filename}`, 'success');
        
    } catch (error) {
        console.error('Error creating backup:', error);
        showStatus(backupStatus, 'Error creating backup: ' + error.message, 'error');
    } finally {
        createBackupBtn.disabled = false;
    }
}

async function showBackupModal() {
    try {
        const response = await fetch('/api/backups');
        if (!response.ok) {
            throw new Error('Failed to load backups');
        }
        
        const backups = await response.json();
        const backupList = document.getElementById('backupList');
        
        if (backups.length === 0) {
            backupList.innerHTML = '<p>No backups found.</p>';
        } else {
            backupList.innerHTML = backups.map(backup => `
                <div class="backup-item">
                    <div class="backup-info">
                        <div class="backup-name">${backup.filename}</div>
                        <div class="backup-details">
                            ${new Date(backup.timestamp).toLocaleString()} • 
                            ${formatFileSize(backup.size)} • 
                            ${backup.description}
                        </div>
                    </div>
                    <div class="backup-actions">
                        <button class="restore-btn" onclick="restoreBackup('${backup.id}')">
                            Restore
                        </button>
                    </div>
                </div>
            `).join('');
        }
        
        document.getElementById('backupModal').style.display = 'flex';
        
    } catch (error) {
        console.error('Error loading backups:', error);
        showStatus(backupStatus, 'Error loading backups: ' + error.message, 'error');
    }
}

function closeBackupModal() {
    document.getElementById('backupModal').style.display = 'none';
}

async function restoreBackup(backupId) {
    if (!confirm('Are you sure you want to restore this backup? This will replace the current database.')) {
        return;
    }
    
    try {
        const response = await fetch(`/api/backups/restore/${backupId}`, {
            method: 'POST'
        });
        
        if (!response.ok) {
            throw new Error('Failed to restore backup');
        }
        
        alert('Backup restored successfully! Please refresh the page.');
        window.location.reload();
        
    } catch (error) {
        console.error('Error restoring backup:', error);
        alert('Error restoring backup: ' + error.message);
    }
}

function formatFileSize(bytes) {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
}

function showStatus(element, message, type) {
    element.textContent = message;
    element.className = `status-message ${type}`;
    element.style.display = 'block';
    
    // Auto-hide success messages after 5 seconds
    if (type === 'success') {
        setTimeout(() => {
            element.style.display = 'none';
        }, 5000);
    }
}

function showConfirmModal(message, onConfirm) {
    document.getElementById('confirmMessage').textContent = message;
    document.getElementById('confirmModal').style.display = 'flex';
    
    document.getElementById('confirmYes').onclick = () => {
        closeConfirmModal();
        onConfirm();
    };
}

function closeConfirmModal() {
    document.getElementById('confirmModal').style.display = 'none';
}

async function processDuplicates() {
    if (groupActions.size === 0) {
        showStatus(processStatus, 'No actions selected', 'error');
        return;
    }

    const actionCount = Array.from(groupActions.values()).filter(a => a.action !== 'skip').length;

    if (actionCount === 0) {
        showStatus(processStatus, 'All groups are set to skip - no changes will be made', 'info');
        return;
    }

    const message = `This will process ${actionCount} duplicate groups and may delete agencies. Are you sure you want to continue?`;

    showConfirmModal(message, async () => {
        try {
            processBtn.disabled = true;
            showStatus(processStatus, 'Processing duplicates...', 'info');

            // Prepare actions array
            const actions = [];
            for (const [groupId, actionData] of groupActions.entries()) {
                const group = duplicateResults.groups.find(g => g.id === groupId);
                if (group) {
                    actions.push({
                        groupId,
                        action: actionData.action,
                        selectedAgencyId: actionData.selectedAgencyId,
                        agencies: group.agencies
                    });
                }
            }

            const response = await fetch('/api/duplicates/process', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ actions })
            });

            if (!response.ok) {
                throw new Error('Failed to process duplicates');
            }

            const result = await response.json();

            // Show results
            const successful = result.results.filter(r => r.success).length;
            const failed = result.results.filter(r => !r.success).length;

            let message = `Processing complete: ${successful} successful`;
            if (failed > 0) {
                message += `, ${failed} failed`;
            }

            showStatus(processStatus, message, failed > 0 ? 'error' : 'success');

            // Show detailed results
            result.results.forEach(res => {
                const statusDiv = document.getElementById(`status-${res.groupId}`);
                if (statusDiv) {
                    if (res.success) {
                        showStatus(statusDiv, res.message || 'Processed successfully', 'success');
                    } else {
                        showStatus(statusDiv, res.error || 'Processing failed', 'error');
                    }
                }
            });

            // Suggest refreshing the main page
            if (successful > 0) {
                setTimeout(() => {
                    if (confirm('Duplicates processed successfully! Would you like to return to the main page to see the updated data?')) {
                        window.location.href = '/';
                    }
                }, 2000);
            }

        } catch (error) {
            console.error('Error processing duplicates:', error);
            showStatus(processStatus, 'Error processing duplicates: ' + error.message, 'error');
        } finally {
            processBtn.disabled = false;
        }
    });
}
