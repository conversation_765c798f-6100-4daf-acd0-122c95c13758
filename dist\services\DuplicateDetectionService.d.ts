import { Agency } from '../models/Agency';
export interface DuplicateDetectionCriteria {
    exactNameMatch: boolean;
    fuzzyNameMatch: boolean;
    fuzzyNameThreshold: number;
    sameWebsite: boolean;
    sameClutchProfile: boolean;
    locationAndSimilarName: boolean;
    locationNameThreshold: number;
}
export interface DuplicateGroup {
    id: string;
    agencies: AgencyWithId[];
    reason: string;
    confidence: number;
}
export interface AgencyWithId extends Agency {
    id: number;
    scraped_at: string;
}
export interface DuplicateDetectionResult {
    groups: DuplicateGroup[];
    totalDuplicates: number;
    totalGroups: number;
    criteria: DuplicateDetectionCriteria;
}
export declare class DuplicateDetectionService {
    private static normalizeString;
    private static normalizeUrl;
    private static generateGroupId;
    static detectDuplicates(agencies: AgencyWithId[], criteria: DuplicateDetectionCriteria): DuplicateDetectionResult;
    private static isMatch;
    private static calculateConfidence;
    static getDefaultCriteria(): DuplicateDetectionCriteria;
    static getBestAgency(agencies: AgencyWithId[]): AgencyWithId;
    static mergeAgencies(agencies: AgencyWithId[]): Partial<AgencyWithId>;
}
//# sourceMappingURL=DuplicateDetectionService.d.ts.map