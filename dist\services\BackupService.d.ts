export interface BackupInfo {
    id: string;
    filename: string;
    timestamp: Date;
    size: number;
    description: string;
}
export interface DuplicateAction {
    id: string;
    timestamp: Date;
    action: 'delete' | 'merge' | 'keep';
    groupId: string;
    agenciesAffected: number[];
    keptAgencyId?: number | undefined;
    description: string;
}
export declare class BackupService {
    private static readonly BACKUP_DIR;
    private static readonly LOG_FILE;
    static ensureBackupDirectory(): Promise<void>;
    static createBackup(description?: string): Promise<BackupInfo>;
    static listBackups(): Promise<BackupInfo[]>;
    static restoreBackup(backupId: string): Promise<void>;
    static logAction(action: DuplicateAction): Promise<void>;
    static getActionLog(): Promise<DuplicateAction[]>;
    static cleanupOldBackups(keepCount?: number): Promise<void>;
    static formatFileSize(bytes: number): string;
    static getDatabaseStats(): Promise<{
        size: number;
        agencyCount: number;
    }>;
}
//# sourceMappingURL=BackupService.d.ts.map