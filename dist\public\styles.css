/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

header h1 {
    color: #2c3e50;
    margin-bottom: 10px;
}

header p {
    color: #7f8c8d;
    font-size: 1.1em;
}

.header-nav {
    margin-top: 15px;
}

.nav-link {
    display: inline-block;
    background-color: #e74c3c;
    color: white;
    padding: 10px 20px;
    text-decoration: none;
    border-radius: 6px;
    font-weight: 600;
    transition: background-color 0.3s;
}

.nav-link:hover {
    background-color: #c0392b;
    text-decoration: none;
}

/* Filters Section */
.filters-section {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.search-row {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 15px;
    margin-bottom: 15px;
}

.search-field {
    display: flex;
    flex-direction: column;
}

.search-field label {
    font-weight: 600;
    margin-bottom: 5px;
    color: #2c3e50;
}

.search-field input,
.search-field select {
    padding: 10px;
    border: 2px solid #e0e0e0;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s;
}

.search-field input:focus,
.search-field select:focus {
    outline: none;
    border-color: #3498db;
}

.filter-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 15px;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.filter-group label {
    font-weight: 600;
    color: #2c3e50;
    min-width: 60px;
}

.filter-group input {
    padding: 8px;
    border: 2px solid #e0e0e0;
    border-radius: 4px;
    width: 80px;
    font-size: 14px;
}

.filter-group input[type="checkbox"] {
    width: auto;
    padding: 0;
    margin-right: 8px;
    transform: scale(1.2);
}

.filter-group span {
    color: #7f8c8d;
}

.button-row {
    display: flex;
    gap: 10px;
}

button {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: background-color 0.3s;
}

button[type="submit"] {
    background-color: #3498db;
    color: white;
}

button[type="submit"]:hover {
    background-color: #2980b9;
}

#clearFilters {
    background-color: #95a5a6;
    color: white;
}

#clearFilters:hover {
    background-color: #7f8c8d;
}

/* Results Summary */
.results-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.view-toggle {
    display: flex;
    gap: 5px;
}

.view-btn {
    padding: 8px 16px;
    background-color: #ecf0f1;
    color: #2c3e50;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
}

.view-btn.active,
.view-btn:hover {
    background-color: #3498db;
    color: white;
}

/* Loading */
.loading {
    text-align: center;
    padding: 40px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Table View */
.table-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow-x: auto;
}

.agencies-table {
    width: 100%;
    border-collapse: collapse;
}

.agencies-table th {
    background-color: #34495e;
    color: white;
    padding: 15px 10px;
    text-align: left;
    font-weight: 600;
    position: sticky;
    top: 0;
}

.agencies-table td {
    padding: 12px 10px;
    border-bottom: 1px solid #ecf0f1;
    vertical-align: top;
}

.agencies-table tr:hover {
    background-color: #f8f9fa;
}

.agency-name a {
    color: #3498db;
    text-decoration: none;
    font-weight: 600;
}

.agency-name a:hover {
    text-decoration: underline;
}

.rating-value {
    color: #f39c12;
    font-weight: 600;
}

.description-text {
    max-width: 300px;
}

.expand-btn {
    background: none;
    border: none;
    color: #3498db;
    cursor: pointer;
    font-size: 12px;
    padding: 0;
    margin-left: 5px;
}

.website a {
    color: #27ae60;
    text-decoration: none;
}

.website a:hover {
    text-decoration: underline;
}

/* Card View */
.cards-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
}

.agency-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 20px;
    transition: transform 0.2s, box-shadow 0.2s;
}

.agency-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.card-header {
    margin-bottom: 15px;
    border-bottom: 1px solid #ecf0f1;
    padding-bottom: 15px;
}

.card-header h3 {
    margin-bottom: 8px;
}

.card-header h3 a {
    color: #2c3e50;
    text-decoration: none;
}

.card-header h3 a:hover {
    color: #3498db;
}

.rating-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.rating-info .rating {
    color: #f39c12;
    font-weight: 600;
}

.rating-info .reviews {
    color: #7f8c8d;
    font-size: 0.9em;
}

.card-body p {
    margin-bottom: 10px;
}

.card-body .location {
    color: #7f8c8d;
    font-weight: 500;
}

.card-body .description {
    color: #2c3e50;
    line-height: 1.5;
}

.card-body .website a {
    color: #27ae60;
    text-decoration: none;
    font-weight: 500;
}

.card-body .website a:hover {
    text-decoration: underline;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-top: 30px;
    padding: 20px;
}

.page-btn {
    padding: 8px 12px;
    background-color: white;
    color: #3498db;
    text-decoration: none;
    border: 2px solid #3498db;
    border-radius: 4px;
    transition: all 0.3s;
}

.page-btn:hover,
.page-btn.current {
    background-color: #3498db;
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .search-row {
        grid-template-columns: 1fr;
    }
    
    .filter-row {
        grid-template-columns: 1fr;
    }
    
    .results-summary {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .table-container {
        font-size: 14px;
    }
    
    .agencies-table th,
    .agencies-table td {
        padding: 8px 5px;
    }
    
    .description-text {
        max-width: 200px;
    }
    
    .cards-container {
        grid-template-columns: 1fr;
    }
    
    .pagination {
        flex-wrap: wrap;
    }
}

@media (max-width: 480px) {
    .agencies-table {
        font-size: 12px;
    }

    .agencies-table th,
    .agencies-table td {
        padding: 6px 3px;
    }

    .description-text {
        max-width: 150px;
    }

    /* Stack table cells vertically on very small screens */
    .agencies-table,
    .agencies-table thead,
    .agencies-table tbody,
    .agencies-table th,
    .agencies-table td,
    .agencies-table tr {
        display: block;
    }

    .agencies-table thead tr {
        position: absolute;
        top: -9999px;
        left: -9999px;
    }

    .agencies-table tr {
        border: 1px solid #ccc;
        margin-bottom: 10px;
        padding: 10px;
        border-radius: 4px;
        background: white;
    }

    .agencies-table td {
        border: none;
        position: relative;
        padding-left: 50% !important;
        padding-top: 8px;
        padding-bottom: 8px;
    }

    .agencies-table td:before {
        content: attr(data-label) ": ";
        position: absolute;
        left: 6px;
        width: 45%;
        padding-right: 10px;
        white-space: nowrap;
        font-weight: bold;
        color: #2c3e50;
    }
}
