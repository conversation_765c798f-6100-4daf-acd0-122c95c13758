"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClutchScrapeCli = void 0;
const commander_1 = require("commander");
const fs_1 = require("fs");
const path_1 = __importDefault(require("path"));
const Database_1 = require("../database/Database");
const FileScanner_1 = require("../scanner/FileScanner");
const HtmlParser_1 = require("../parser/HtmlParser");
class ClutchScrapeCli {
    constructor() {
        this.program = new commander_1.Command();
        this.setupCommands();
    }
    setupCommands() {
        this.program
            .name('clutch-scrape-ts')
            .description('Process local HTML files to extract agency data and store results in SQLite')
            .version('1.0.0');
        this.program
            .option('--db <path>', `SQLite DB path (default: ${Database_1.DEFAULT_DB_PATH})`, Database_1.DEFAULT_DB_PATH)
            .option('--verbose', 'Enable verbose logging', false);
        this.program
            .command('process')
            .description('Process HTML files from input folder and store results in the database')
            .option('--input-folder <path>', 'Input folder containing HTML files', 'input')
            .option('--min-rating <rating>', 'Minimum rating filter (e.g., 4.5)', parseFloat)
            .option('--has-website', 'Only process agencies with websites')
            .option('--export <path>', 'Optional export path (.csv or .json)')
            .action(async (options) => {
            const globalOptions = this.program.opts();
            await this.processCommand({
                ...globalOptions,
                ...options
            });
        });
        this.program
            .command('stats')
            .description('Show basic statistics about processed data')
            .action(async () => {
            const globalOptions = this.program.opts();
            await this.statsCommand(globalOptions);
        });
    }
    configureLogging(verbose) {
        if (verbose) {
            console.log('Verbose logging enabled');
        }
    }
    async processCommand(options) {
        this.configureLogging(options.verbose);
        try {
            const scanner = new FileScanner_1.FileScanner({
                inputFolder: options.inputFolder,
                fileExtensions: ['.html', '.htm'],
                recursive: false
            });
            const parser = new HtmlParser_1.HtmlParser();
            const database = new Database_1.Database(options.db);
            if (!(await scanner.validateInputFolder())) {
                console.error(`Input folder '${options.inputFolder}' does not exist`);
                console.log(`Please create the folder and add HTML files to process`);
                process.exit(1);
            }
            console.log(`Scanning for HTML files in '${options.inputFolder}'...`);
            const htmlFiles = await scanner.scanForHtmlFiles();
            if (htmlFiles.length === 0) {
                console.log(`No HTML files found in '${options.inputFolder}'`);
                console.log(`Supported extensions: ${scanner.getSupportedExtensions().join(', ')}`);
                return;
            }
            console.log(`Found ${htmlFiles.length} HTML file(s) to process`);
            const allAgencies = [];
            for (const filePath of htmlFiles) {
                console.log(`Processing: ${filePath}`);
                try {
                    const parseOptions = {};
                    if (options.minRating !== undefined) {
                        parseOptions.minRating = options.minRating;
                    }
                    let agencies = await parser.parseFile(filePath, parseOptions);
                    if (options.hasWebsite) {
                        const beforeCount = agencies.length;
                        agencies = agencies.filter(agency => agency.contactWebsite &&
                            agency.contactWebsite.trim() !== '');
                        console.log(`  Filtered to ${agencies.length} agencies with websites (removed ${beforeCount - agencies.length})`);
                    }
                    else {
                        console.log(`  Extracted ${agencies.length} agencies`);
                    }
                    allAgencies.push(...agencies);
                }
                catch (error) {
                    console.error(`  Error processing ${filePath}: ${error.message}`);
                }
            }
            if (allAgencies.length === 0) {
                console.log('No agencies extracted from HTML files');
                return;
            }
            console.log(`Storing ${allAgencies.length} agencies in database...`);
            const upsertedCount = await database.upsertAgencies(allAgencies);
            console.log(`Upserted ${upsertedCount} agencies into ${options.db}`);
            if (options.export) {
                await this.exportData(allAgencies, options.export);
            }
            database.close();
        }
        catch (error) {
            console.error('Error during processing:', error.message);
            process.exit(1);
        }
    }
    async statsCommand(options) {
        this.configureLogging(options.verbose);
        try {
            const database = new Database_1.Database(options.db);
            const stats = await database.getStats();
            console.log(JSON.stringify(stats, null, 2));
            database.close();
        }
        catch (error) {
            console.error('Error getting stats:', error.message);
            process.exit(1);
        }
    }
    async exportData(agencies, exportPath) {
        const ext = path_1.default.extname(exportPath).toLowerCase();
        try {
            if (ext === '.json') {
                await fs_1.promises.writeFile(exportPath, JSON.stringify(agencies, null, 2), 'utf-8');
                console.log(`Exported ${agencies.length} rows to ${exportPath}`);
            }
            else if (ext === '.csv') {
                await this.exportToCsv(agencies, exportPath);
                console.log(`Exported ${agencies.length} rows to ${exportPath}`);
            }
            else {
                console.warn(`Unsupported export extension: ${ext} (use .csv or .json)`);
            }
        }
        catch (error) {
            console.error(`Failed to export to ${exportPath}: ${error.message}`);
        }
    }
    async exportToCsv(agencies, filePath) {
        const headers = [
            'name',
            'rating',
            'review_count',
            'location',
            'description',
            'contact_phone',
            'contact_website',
            'clutch_profile_url'
        ];
        const csvLines = [headers.join(',')];
        for (const agency of agencies) {
            const row = [
                this.escapeCsvField(agency.name),
                agency.rating?.toString() || '',
                agency.reviewCount?.toString() || '',
                this.escapeCsvField(agency.location),
                this.escapeCsvField(agency.description),
                this.escapeCsvField(agency.contactPhone),
                this.escapeCsvField(agency.contactWebsite),
                this.escapeCsvField(agency.clutchProfileUrl)
            ];
            csvLines.push(row.join(','));
        }
        await fs_1.promises.writeFile(filePath, csvLines.join('\n'), 'utf-8');
    }
    escapeCsvField(value) {
        if (value === null) {
            return '';
        }
        if (value.includes(',') || value.includes('"') || value.includes('\n')) {
            return `"${value.replace(/"/g, '""')}"`;
        }
        return value;
    }
    async run(argv) {
        await this.program.parseAsync(argv);
    }
}
exports.ClutchScrapeCli = ClutchScrapeCli;
//# sourceMappingURL=cli.js.map