<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Duplicate Management - Clutch Agencies Explorer</title>
    <link rel="stylesheet" href="/styles.css">
    <link rel="stylesheet" href="/duplicates.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🔍 Duplicate Management</h1>
            <p>Detect and manage duplicate agency entries</p>
            <nav class="breadcrumb">
                <a href="/">← Back to Main</a>
            </nav>
        </header>

        <!-- Detection Criteria -->
        <div class="detection-section">
            <h2>Detection Criteria</h2>
            <form id="criteriaForm" class="criteria-form">
                <div class="criteria-grid">
                    <div class="criteria-group">
                        <h3>Name Matching</h3>
                        <label class="checkbox-label">
                            <input type="checkbox" id="exactNameMatch" checked>
                            <span>Exact name matches (case-insensitive)</span>
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" id="fuzzyNameMatch" checked>
                            <span>Similar names (fuzzy matching)</span>
                        </label>
                        <div class="threshold-input">
                            <label for="fuzzyNameThreshold">Name similarity threshold:</label>
                            <input type="range" id="fuzzyNameThreshold" min="50" max="100" value="85">
                            <span id="fuzzyNameValue">85%</span>
                        </div>
                    </div>

                    <div class="criteria-group">
                        <h3>URL Matching</h3>
                        <label class="checkbox-label">
                            <input type="checkbox" id="sameWebsite" checked>
                            <span>Same website URL</span>
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" id="sameClutchProfile" checked>
                            <span>Same Clutch profile URL</span>
                        </label>
                    </div>

                    <div class="criteria-group">
                        <h3>Location + Name</h3>
                        <label class="checkbox-label">
                            <input type="checkbox" id="locationAndSimilarName" checked>
                            <span>Same location + similar name</span>
                        </label>
                        <div class="threshold-input">
                            <label for="locationNameThreshold">Location+name threshold:</label>
                            <input type="range" id="locationNameThreshold" min="50" max="100" value="80">
                            <span id="locationNameValue">80%</span>
                        </div>
                    </div>
                </div>

                <div class="action-buttons">
                    <button type="button" id="detectBtn" class="primary-btn">🔍 Detect Duplicates</button>
                    <button type="button" id="resetCriteriaBtn" class="secondary-btn">Reset to Defaults</button>
                </div>
            </form>
        </div>

        <!-- Loading Indicator -->
        <div id="loadingIndicator" class="loading-section" style="display: none;">
            <div class="spinner"></div>
            <p>Detecting duplicates...</p>
        </div>

        <!-- Results Section -->
        <div id="resultsSection" class="results-section" style="display: none;">
            <div class="results-header">
                <h2>Detection Results</h2>
                <div class="results-summary">
                    <span id="resultsSummary"></span>
                </div>
            </div>

            <!-- Backup Section -->
            <div class="backup-section">
                <h3>⚠️ Safety First</h3>
                <p>Before processing duplicates, create a backup of your database:</p>
                <div class="backup-controls">
                    <input type="text" id="backupDescription" placeholder="Backup description (optional)" 
                           value="Before duplicate processing">
                    <button type="button" id="createBackupBtn" class="backup-btn">💾 Create Backup</button>
                    <button type="button" id="viewBackupsBtn" class="secondary-btn">📋 View Backups</button>
                </div>
                <div id="backupStatus" class="status-message"></div>
            </div>

            <!-- Bulk Actions -->
            <div class="bulk-actions">
                <h3>Bulk Actions</h3>
                <div class="bulk-controls">
                    <select id="bulkAction">
                        <option value="">Select bulk action...</option>
                        <option value="keep_best">Keep Best (highest rating/reviews)</option>
                        <option value="skip">Skip All Groups</option>
                    </select>
                    <button type="button" id="applyBulkBtn" class="bulk-btn" disabled>Apply to All Groups</button>
                </div>
            </div>

            <!-- Duplicate Groups -->
            <div id="duplicateGroups" class="duplicate-groups">
                <!-- Groups will be populated here -->
            </div>

            <!-- Process Actions -->
            <div class="process-section">
                <button type="button" id="processBtn" class="process-btn" disabled>
                    🚀 Process Selected Actions
                </button>
                <div id="processStatus" class="status-message"></div>
            </div>
        </div>

        <!-- Backup Modal -->
        <div id="backupModal" class="modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Database Backups</h3>
                    <button type="button" class="close-btn" onclick="closeBackupModal()">&times;</button>
                </div>
                <div class="modal-body">
                    <div id="backupList">
                        <!-- Backup list will be populated here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Confirmation Modal -->
        <div id="confirmModal" class="modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Confirm Action</h3>
                </div>
                <div class="modal-body">
                    <p id="confirmMessage"></p>
                    <div class="confirm-actions">
                        <button type="button" id="confirmYes" class="danger-btn">Yes, Proceed</button>
                        <button type="button" id="confirmNo" class="secondary-btn">Cancel</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="/duplicates.js"></script>
</body>
</html>
