"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const express = require('express');
const path = __importStar(require("path"));
const Database_1 = require("../database/Database");
const DuplicateDetectionService_1 = require("../services/DuplicateDetectionService");
const BackupService_1 = require("../services/BackupService");
const app = express();
const PORT = process.env.PORT || 3000;
const db = new Database_1.Database();
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, '../views'));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, '../public')));
function buildSearchQuery(params) {
    let query = 'SELECT * FROM agencies WHERE 1=1';
    const values = [];
    if (params.search) {
        query += ` AND (
      name LIKE ? OR 
      description LIKE ? OR 
      location LIKE ? OR 
      contact_website LIKE ?
    )`;
        const searchTerm = `%${params.search}%`;
        values.push(searchTerm, searchTerm, searchTerm, searchTerm);
    }
    if (params.location) {
        query += ' AND location LIKE ?';
        values.push(`%${params.location}%`);
    }
    if (params.minRating !== undefined) {
        query += ' AND rating >= ?';
        values.push(params.minRating);
    }
    if (params.maxRating !== undefined) {
        query += ' AND rating <= ?';
        values.push(params.maxRating);
    }
    if (params.minReviews !== undefined) {
        query += ' AND review_count >= ?';
        values.push(params.minReviews);
    }
    if (params.maxReviews !== undefined) {
        query += ' AND review_count <= ?';
        values.push(params.maxReviews);
    }
    query += ' ORDER BY rating DESC, review_count DESC';
    const limit = params.limit || 1000;
    const offset = ((params.page || 1) - 1) * limit;
    query += ' LIMIT ? OFFSET ?';
    values.push(limit, offset);
    return { query, values };
}
function buildCountQuery(params) {
    let query = 'SELECT COUNT(*) as total FROM agencies WHERE 1=1';
    const values = [];
    if (params.search) {
        query += ` AND (
      name LIKE ? OR 
      description LIKE ? OR 
      location LIKE ? OR 
      contact_website LIKE ?
    )`;
        const searchTerm = `%${params.search}%`;
        values.push(searchTerm, searchTerm, searchTerm, searchTerm);
    }
    if (params.location) {
        query += ' AND location LIKE ?';
        values.push(`%${params.location}%`);
    }
    if (params.minRating !== undefined) {
        query += ' AND rating >= ?';
        values.push(params.minRating);
    }
    if (params.maxRating !== undefined) {
        query += ' AND rating <= ?';
        values.push(params.maxRating);
    }
    if (params.minReviews !== undefined) {
        query += ' AND review_count >= ?';
        values.push(params.minReviews);
    }
    if (params.maxReviews !== undefined) {
        query += ' AND review_count <= ?';
        values.push(params.maxReviews);
    }
    if (params.hasWebsite === true) {
        query += ' AND contact_website IS NOT NULL AND TRIM(contact_website) <> ""';
    }
    return { query, values };
}
app.get('/', async (req, res) => {
    try {
        const params = {
            search: req.query.search,
            location: req.query.location,
            minRating: req.query.minRating ? parseFloat(req.query.minRating) : undefined,
            maxRating: req.query.maxRating ? parseFloat(req.query.maxRating) : undefined,
            minReviews: req.query.minReviews ? parseInt(req.query.minReviews) : undefined,
            maxReviews: req.query.maxReviews ? parseInt(req.query.maxReviews) : undefined,
            hasWebsite: req.query.hasWebsite === 'true',
            page: req.query.page ? parseInt(req.query.page) : 1,
            limit: 1000
        };
        const { query, values } = buildSearchQuery(params);
        const { query: countQuery, values: countValues } = buildCountQuery(params);
        const agencies = await new Promise((resolve, reject) => {
            db.db.all(query, values, (err, rows) => {
                if (err)
                    reject(err);
                else
                    resolve(rows);
            });
        });
        const totalResult = await new Promise((resolve, reject) => {
            db.db.get(countQuery, countValues, (err, row) => {
                if (err)
                    reject(err);
                else
                    resolve(row);
            });
        });
        const locations = await new Promise((resolve, reject) => {
            db.db.all('SELECT DISTINCT location FROM agencies WHERE location IS NOT NULL AND TRIM(location) <> "" ORDER BY location', (err, rows) => {
                if (err)
                    reject(err);
                else
                    resolve(rows.map((row) => row.location));
            });
        });
        const totalPages = Math.ceil(totalResult.total / params.limit);
        res.render('index', {
            agencies,
            locations,
            params,
            totalPages,
            currentPage: params.page,
            totalResults: totalResult.total
        });
    }
    catch (error) {
        console.error('Error fetching agencies:', error);
        res.status(500).send('Internal Server Error');
    }
});
app.get('/api/agencies', async (req, res) => {
    try {
        const params = {
            search: req.query.search,
            location: req.query.location,
            minRating: req.query.minRating ? parseFloat(req.query.minRating) : undefined,
            maxRating: req.query.maxRating ? parseFloat(req.query.maxRating) : undefined,
            minReviews: req.query.minReviews ? parseInt(req.query.minReviews) : undefined,
            maxReviews: req.query.maxReviews ? parseInt(req.query.maxReviews) : undefined,
            hasWebsite: req.query.hasWebsite === 'true',
            page: req.query.page ? parseInt(req.query.page) : 1,
            limit: 1000
        };
        const { query, values } = buildSearchQuery(params);
        const { query: countQuery, values: countValues } = buildCountQuery(params);
        const agencies = await new Promise((resolve, reject) => {
            db.db.all(query, values, (err, rows) => {
                if (err)
                    reject(err);
                else
                    resolve(rows);
            });
        });
        const totalResult = await new Promise((resolve, reject) => {
            db.db.get(countQuery, countValues, (err, row) => {
                if (err)
                    reject(err);
                else
                    resolve(row);
            });
        });
        const totalPages = Math.ceil(totalResult.total / params.limit);
        res.json({
            agencies,
            totalPages,
            currentPage: params.page,
            totalResults: totalResult.total
        });
    }
    catch (error) {
        console.error('Error fetching agencies:', error);
        res.status(500).json({ error: 'Internal Server Error' });
    }
});
app.get('/duplicates', (req, res) => {
    res.render('duplicates', {
        title: 'Duplicate Management'
    });
});
app.post('/api/duplicates/detect', async (req, res) => {
    try {
        const criteria = {
            exactNameMatch: req.body.exactNameMatch === true,
            fuzzyNameMatch: req.body.fuzzyNameMatch === true,
            fuzzyNameThreshold: parseInt(req.body.fuzzyNameThreshold) || 85,
            sameWebsite: req.body.sameWebsite === true,
            sameClutchProfile: req.body.sameClutchProfile === true,
            locationAndSimilarName: req.body.locationAndSimilarName === true,
            locationNameThreshold: parseInt(req.body.locationNameThreshold) || 80
        };
        const agencies = await new Promise((resolve, reject) => {
            db.db.all('SELECT * FROM agencies ORDER BY name', (err, rows) => {
                if (err)
                    reject(err);
                else
                    resolve(rows);
            });
        });
        const result = DuplicateDetectionService_1.DuplicateDetectionService.detectDuplicates(agencies, criteria);
        res.json(result);
    }
    catch (error) {
        console.error('Error detecting duplicates:', error);
        res.status(500).json({ error: 'Failed to detect duplicates' });
    }
});
app.post('/api/backups/create', async (req, res) => {
    try {
        const description = req.body.description || 'Manual backup before duplicate processing';
        const backup = await BackupService_1.BackupService.createBackup(description);
        res.json(backup);
    }
    catch (error) {
        console.error('Error creating backup:', error);
        res.status(500).json({ error: 'Failed to create backup' });
    }
});
app.get('/api/backups', async (req, res) => {
    try {
        const backups = await BackupService_1.BackupService.listBackups();
        res.json(backups);
    }
    catch (error) {
        console.error('Error listing backups:', error);
        res.status(500).json({ error: 'Failed to list backups' });
    }
});
app.post('/api/backups/restore/:backupId', async (req, res) => {
    try {
        const backupId = req.params.backupId;
        if (!backupId) {
            return res.status(400).json({ error: 'Backup ID is required' });
        }
        await BackupService_1.BackupService.restoreBackup(backupId);
        return res.json({ success: true, message: 'Backup restored successfully' });
    }
    catch (error) {
        console.error('Error restoring backup:', error);
        return res.status(500).json({ error: 'Failed to restore backup' });
    }
});
app.post('/api/duplicates/process', async (req, res) => {
    try {
        const { actions } = req.body;
        if (!Array.isArray(actions)) {
            return res.status(400).json({ error: 'Actions must be an array' });
        }
        const results = [];
        for (const actionItem of actions) {
            const { groupId, action, selectedAgencyId, agencies } = actionItem;
            if (!groupId || !action || !Array.isArray(agencies)) {
                results.push({ groupId, success: false, error: 'Invalid action data' });
                continue;
            }
            try {
                let keptAgencyId;
                const agencyIds = agencies.map((a) => a.id);
                if (action === 'keep_best') {
                    const bestAgency = DuplicateDetectionService_1.DuplicateDetectionService.getBestAgency(agencies);
                    keptAgencyId = bestAgency.id;
                    const toDelete = agencyIds.filter(id => id !== keptAgencyId);
                    await DatabaseHelpers.deleteAgencies(toDelete);
                }
                else if (action === 'keep_selected') {
                    if (!selectedAgencyId) {
                        results.push({ groupId, success: false, error: 'No agency selected' });
                        continue;
                    }
                    keptAgencyId = selectedAgencyId;
                    const toDelete = agencyIds.filter(id => id !== selectedAgencyId);
                    await DatabaseHelpers.deleteAgencies(toDelete);
                }
                else if (action === 'merge') {
                    const mergedData = DuplicateDetectionService_1.DuplicateDetectionService.mergeAgencies(agencies);
                    keptAgencyId = mergedData.id;
                    await DatabaseHelpers.updateAgency(mergedData);
                    const toDelete = agencyIds.filter(id => id !== keptAgencyId);
                    await DatabaseHelpers.deleteAgencies(toDelete);
                }
                else if (action === 'skip') {
                    results.push({ groupId, success: true, message: 'Group skipped' });
                    continue;
                }
                await BackupService_1.BackupService.logAction({
                    id: `action_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`,
                    timestamp: new Date(),
                    action: action === 'skip' ? 'keep' : 'delete',
                    groupId,
                    agenciesAffected: agencyIds,
                    keptAgencyId,
                    description: `${action} action on duplicate group ${groupId}`
                });
                results.push({ groupId, success: true, keptAgencyId });
            }
            catch (error) {
                console.error(`Error processing group ${groupId}:`, error);
                results.push({ groupId, success: false, error: error.message });
            }
        }
        return res.json({ results });
    }
    catch (error) {
        console.error('Error processing duplicates:', error);
        return res.status(500).json({ error: 'Failed to process duplicates' });
    }
});
const DatabaseHelpers = {
    async deleteAgencies(agencyIds) {
        if (agencyIds.length === 0)
            return;
        const placeholders = agencyIds.map(() => '?').join(',');
        const query = `DELETE FROM agencies WHERE id IN (${placeholders})`;
        return new Promise((resolve, reject) => {
            db.db.run(query, agencyIds, (err) => {
                if (err)
                    reject(err);
                else
                    resolve();
            });
        });
    },
    async updateAgency(agencyData) {
        const query = `
      UPDATE agencies SET
        name = ?, rating = ?, review_count = ?, location = ?, description = ?,
        contact_phone = ?, contact_website = ?, clutch_profile_url = ?,
        data_json = ?, scraped_at = ?
      WHERE id = ?
    `;
        return new Promise((resolve, reject) => {
            db.db.run(query, [
                agencyData.name,
                agencyData.rating,
                agencyData.reviewCount,
                agencyData.location,
                agencyData.description,
                agencyData.contactPhone,
                agencyData.contactWebsite,
                agencyData.clutchProfileUrl,
                JSON.stringify(agencyData.dataJson),
                agencyData.scraped_at,
                agencyData.id
            ], (err) => {
                if (err)
                    reject(err);
                else
                    resolve();
            });
        });
    }
};
app.get('/api/duplicates/log', async (req, res) => {
    try {
        const log = await BackupService_1.BackupService.getActionLog();
        res.json(log);
    }
    catch (error) {
        console.error('Error getting action log:', error);
        res.status(500).json({ error: 'Failed to get action log' });
    }
});
app.get('/stats', async (req, res) => {
    try {
        const stats = await db.getStats();
        res.render('stats', { stats });
    }
    catch (error) {
        console.error('Error fetching statistics:', error);
        res.status(500).send('Internal Server Error');
    }
});
app.listen(PORT, () => {
    console.log(`Server running on http://localhost:${PORT}`);
});
exports.default = app;
//# sourceMappingURL=server.js.map