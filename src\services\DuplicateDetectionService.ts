const fuzzball = require('fuzzball');
import { Agency } from '../models/Agency';

export interface DuplicateDetectionCriteria {
  exactNameMatch: boolean;
  fuzzyNameMatch: boolean;
  fuzzyNameThreshold: number; // 0-100
  sameWebsite: boolean;
  sameClutchProfile: boolean;
  locationAndSimilarName: boolean;
  locationNameThreshold: number; // 0-100
}

export interface DuplicateGroup {
  id: string;
  agencies: AgencyWithId[];
  reason: string;
  confidence: number;
}

export interface AgencyWithId extends Agency {
  id: number;
  scraped_at: string;
}

export interface DuplicateDetectionResult {
  groups: DuplicateGroup[];
  totalDuplicates: number;
  totalGroups: number;
  criteria: DuplicateDetectionCriteria;
}

export class DuplicateDetectionService {
  private static normalizeString(str: string | null): string {
    if (!str) return '';
    return str.toLowerCase().trim().replace(/\s+/g, ' ');
  }

  private static normalizeUrl(url: string | null): string {
    if (!url) return '';
    try {
      const normalized = url.toLowerCase().trim();
      // Remove protocol and www
      return normalized.replace(/^https?:\/\/(www\.)?/, '').replace(/\/$/, '');
    } catch {
      return url.toLowerCase().trim();
    }
  }

  private static generateGroupId(): string {
    return 'dup_' + Math.random().toString(36).substr(2, 9);
  }

  public static detectDuplicates(
    agencies: AgencyWithId[],
    criteria: DuplicateDetectionCriteria
  ): DuplicateDetectionResult {
    const groups: DuplicateGroup[] = [];
    const processed = new Set<number>();

    for (let i = 0; i < agencies.length; i++) {
      const currentAgency = agencies[i];
      if (!currentAgency || processed.has(currentAgency.id)) continue;

      const duplicates: AgencyWithId[] = [currentAgency];
      const reasons: string[] = [];

      for (let j = i + 1; j < agencies.length; j++) {
        const compareAgency = agencies[j];
        if (!compareAgency || processed.has(compareAgency.id)) continue;

        const matchResult = this.isMatch(currentAgency, compareAgency, criteria);

        if (matchResult.isMatch) {
          duplicates.push(compareAgency);
          if (!reasons.includes(matchResult.reason)) {
            reasons.push(matchResult.reason);
          }
        }
      }

      if (duplicates.length > 1) {
        // Mark all agencies in this group as processed
        duplicates.forEach(agency => processed.add(agency.id));

        groups.push({
          id: this.generateGroupId(),
          agencies: duplicates,
          reason: reasons.join(', '),
          confidence: this.calculateConfidence(duplicates, reasons)
        });
      }
    }

    return {
      groups,
      totalDuplicates: groups.reduce((sum, group) => sum + group.agencies.length, 0),
      totalGroups: groups.length,
      criteria
    };
  }

  private static isMatch(
    agency1: AgencyWithId,
    agency2: AgencyWithId,
    criteria: DuplicateDetectionCriteria
  ): { isMatch: boolean; reason: string; confidence: number } {
    const reasons: string[] = [];
    let maxConfidence = 0;

    // Exact name match
    if (criteria.exactNameMatch && agency1.name && agency2.name) {
      const name1 = this.normalizeString(agency1.name);
      const name2 = this.normalizeString(agency2.name);
      if (name1 === name2 && name1.length > 0) {
        reasons.push('Exact name match');
        maxConfidence = Math.max(maxConfidence, 100);
      }
    }

    // Fuzzy name match
    if (criteria.fuzzyNameMatch && agency1.name && agency2.name) {
      const similarity = fuzzball.ratio(
        this.normalizeString(agency1.name),
        this.normalizeString(agency2.name)
      );
      if (similarity >= criteria.fuzzyNameThreshold) {
        reasons.push(`Similar name (${similarity}% match)`);
        maxConfidence = Math.max(maxConfidence, similarity);
      }
    }

    // Same website
    if (criteria.sameWebsite && agency1.contactWebsite && agency2.contactWebsite) {
      const url1 = this.normalizeUrl(agency1.contactWebsite);
      const url2 = this.normalizeUrl(agency2.contactWebsite);
      if (url1 === url2 && url1.length > 0) {
        reasons.push('Same website');
        maxConfidence = Math.max(maxConfidence, 95);
      }
    }

    // Same Clutch profile
    if (criteria.sameClutchProfile && agency1.clutchProfileUrl && agency2.clutchProfileUrl) {
      const profile1 = this.normalizeUrl(agency1.clutchProfileUrl);
      const profile2 = this.normalizeUrl(agency2.clutchProfileUrl);
      if (profile1 === profile2 && profile1.length > 0) {
        reasons.push('Same Clutch profile');
        maxConfidence = Math.max(maxConfidence, 100);
      }
    }

    // Location and similar name
    if (criteria.locationAndSimilarName && agency1.location && agency2.location && agency1.name && agency2.name) {
      const location1 = this.normalizeString(agency1.location);
      const location2 = this.normalizeString(agency2.location);
      
      if (location1 === location2 && location1.length > 0) {
        const nameSimilarity = fuzzball.ratio(
          this.normalizeString(agency1.name),
          this.normalizeString(agency2.name)
        );
        if (nameSimilarity >= criteria.locationNameThreshold) {
          reasons.push(`Same location + similar name (${nameSimilarity}% match)`);
          maxConfidence = Math.max(maxConfidence, nameSimilarity * 0.9); // Slightly lower confidence
        }
      }
    }

    return {
      isMatch: reasons.length > 0,
      reason: reasons.join(', '),
      confidence: maxConfidence
    };
  }

  private static calculateConfidence(agencies: AgencyWithId[], reasons: string[]): number {
    // Calculate overall confidence based on the number of agencies and match reasons
    let baseConfidence = 70;
    
    if (reasons.some(r => r.includes('Exact name match') || r.includes('Same Clutch profile'))) {
      baseConfidence = 95;
    } else if (reasons.some(r => r.includes('Same website'))) {
      baseConfidence = 90;
    } else if (reasons.some(r => r.includes('Similar name'))) {
      const percentMatch = reasons
        .filter(r => r.includes('% match)'))
        .map(r => parseInt(r.match(/(\d+)% match/)?.[1] || '0'))
        .reduce((max, val) => Math.max(max, val), 0);
      baseConfidence = Math.max(70, percentMatch * 0.8);
    }

    // Adjust based on number of duplicates (more duplicates = higher confidence)
    const duplicateBonus = Math.min(10, (agencies.length - 2) * 2);
    
    return Math.min(100, baseConfidence + duplicateBonus);
  }

  public static getDefaultCriteria(): DuplicateDetectionCriteria {
    return {
      exactNameMatch: true,
      fuzzyNameMatch: true,
      fuzzyNameThreshold: 85,
      sameWebsite: true,
      sameClutchProfile: true,
      locationAndSimilarName: true,
      locationNameThreshold: 80
    };
  }

  public static getBestAgency(agencies: AgencyWithId[]): AgencyWithId {
    // Priority: highest rating, then most reviews, then most recent scrape
    return agencies.reduce((best, current) => {
      // Compare ratings
      const bestRating = best.rating || 0;
      const currentRating = current.rating || 0;
      
      if (currentRating > bestRating) return current;
      if (currentRating < bestRating) return best;
      
      // Same rating, compare review counts
      const bestReviews = best.reviewCount || 0;
      const currentReviews = current.reviewCount || 0;
      
      if (currentReviews > bestReviews) return current;
      if (currentReviews < bestReviews) return best;
      
      // Same reviews, compare scrape dates (more recent is better)
      const bestDate = new Date(best.scraped_at || 0);
      const currentDate = new Date(current.scraped_at || 0);
      
      return currentDate > bestDate ? current : best;
    });
  }

  public static mergeAgencies(agencies: AgencyWithId[]): Partial<AgencyWithId> {
    const best = this.getBestAgency(agencies);
    
    return {
      id: best.id, // Keep the ID of the best agency
      name: best.name,
      rating: Math.max(...agencies.map(a => a.rating || 0)),
      reviewCount: Math.max(...agencies.map(a => a.reviewCount || 0)),
      location: best.location,
      description: agencies
        .map(a => a.description)
        .filter(d => d && d.length > 0)
        .reduce((longest, current) => 
          (current?.length || 0) > (longest?.length || 0) ? current : longest, ''),
      contactPhone: best.contactPhone,
      contactWebsite: best.contactWebsite,
      clutchProfileUrl: best.clutchProfileUrl,
      dataJson: best.dataJson,
      scraped_at: agencies
        .map(a => new Date(a.scraped_at || 0))
        .reduce((latest, current) => current > latest ? current : latest)
        .toISOString()
    };
  }
}
